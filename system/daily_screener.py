#!/usr/bin/env python3
"""
Daily RSI Oversold Screener
============================

Daily screening tool for RSI < 25 signals using the proven RSI Oversold Bounce strategy.

Usage:
    python3 daily_screener.py

Features:
- Screens top liquid stocks for RSI < 25
- Generates trade plans with position sizing
- Saves results to daily_results/
- Provides execution checklist
"""

import sys
import os
import json
from datetime import datetime
from rsi_oversold_strategy import RSIOversoldStrategy

def save_screening_results(signals: list, trade_plans: list):
    """Save screening results to daily_results directory."""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Ensure daily_results directory exists
    os.makedirs('daily_results', exist_ok=True)
    
    results = {
        'timestamp': timestamp,
        'date': datetime.now().strftime('%Y-%m-%d'),
        'strategy': 'RSI Oversold Bounce',
        'research_basis': {
            'win_rate': 58.7,
            'profit_factor': 2.52,
            'avg_return': 2.03,
            'sample_size': 5256,
            'research_date': '2025-06-09'
        },
        'signals_found': len(signals),
        'signals': signals,
        'trade_plans': trade_plans
    }
    
    filename = f"daily_results/rsi_screening_{timestamp}.json"
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"💾 Results saved to: {filename}")
    return filename

def main():
    """Run daily RSI oversold screening."""
    print("=" * 80)
    print("📅 DAILY RSI OVERSOLD SCREENER")
    print("Strategy: 58.7% Win Rate | 2.52 Profit Factor | 2.03% Avg Return")
    print("Research: 5 Years | 150 Stocks | 5,256 Trades | June 9, 2025")
    print("=" * 80)
    
    # Initialize strategy
    strategy = RSIOversoldStrategy()
    
    # Extended screening universe for daily screening
    screening_universe = [
        # Technology
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'ADBE', 'CRM',
        'ORCL', 'CSCO', 'INTC', 'AMD', 'QCOM', 'TXN', 'AVGO', 'INTU', 'NOW', 'PANW',
        
        # Financial
        'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SPGI', 'V',
        'MA', 'COF', 'USB', 'TFC', 'PNC', 'SCHW', 'CB', 'MMC', 'AON', 'ICE',
        
        # Healthcare
        'UNH', 'JNJ', 'PFE', 'ABBV', 'MRK', 'TMO', 'ABT', 'LLY', 'MDT', 'BMY',
        'AMGN', 'GILD', 'CVS', 'CI', 'HUM', 'SYK', 'BSX', 'EW', 'ZTS', 'REGN',
        
        # Consumer Discretionary
        'HD', 'MCD', 'NKE', 'LOW', 'SBUX', 'TJX', 'BKNG', 'CMG', 'ORLY', 'AZO',
        'ULTA', 'RCL', 'CCL', 'MAR', 'HLT', 'MGM', 'WYNN', 'LVS', 'YUM', 'QSR',
        
        # Consumer Staples
        'WMT', 'PG', 'KO', 'PEP', 'COST', 'CL', 'KMB', 'GIS', 'K', 'HSY',
        'MDLZ', 'CPB', 'CAG', 'SJM', 'HRL', 'TSN', 'TAP', 'STZ', 'PM', 'MO'
    ]
    
    # Screen for signals
    signals = strategy.screen_for_signals(screening_universe)
    strategy.display_signals(signals)
    
    # Generate trade plans for all signals
    trade_plans = []
    portfolio_value = 813.65  # Update with actual portfolio value
    
    if signals:
        print(f"\n📋 GENERATING TRADE PLANS FOR {len(signals)} SIGNALS...")
        
        for i, signal in enumerate(signals[:5]):  # Limit to top 5 signals
            trade_plan = strategy.generate_trade_plan(signal, portfolio_value)
            trade_plans.append(trade_plan)
            
            print(f"\n🎯 TRADE PLAN #{i+1}: {signal['ticker']}")
            strategy.display_trade_plan(trade_plan)
    
    # Save results
    save_screening_results(signals, trade_plans)
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 DAILY SCREENING SUMMARY")
    print("=" * 80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔍 Stocks Screened: {len(screening_universe)}")
    print(f"🎯 Signals Found: {len(signals)}")
    print(f"📋 Trade Plans Generated: {len(trade_plans)}")
    
    if signals:
        best_signal = signals[0]
        print(f"\n🏆 BEST OPPORTUNITY:")
        print(f"   Ticker: {best_signal['ticker']}")
        print(f"   RSI: {best_signal['rsi']:.1f}")
        print(f"   Signal Strength: {best_signal['signal_strength']}")
        print(f"   Expected Return: {best_signal['expected_return']:.2f}%")
        print(f"   Win Probability: {best_signal['win_probability']:.1f}%")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Review trade plan for {best_signal['ticker']}")
        print(f"   2. Prepare to buy at market open")
        print(f"   3. Set stop loss at 2% below entry")
        print(f"   4. Set calendar reminder to sell in 2 days")
        print(f"   5. Expected profit: ~2.03% (${portfolio_value * 0.0203:.2f})")
    else:
        print(f"\n❌ NO SIGNALS FOUND TODAY")
        print(f"   No stocks with RSI < 25")
        print(f"   Check again tomorrow")
        print(f"   Typical frequency: 4-8 signals per month")
    
    print(f"\n✅ Daily screening completed!")
    print(f"💡 Remember: This strategy has 58.7% win rate based on 5,256 real trades")


if __name__ == "__main__":
    main()
