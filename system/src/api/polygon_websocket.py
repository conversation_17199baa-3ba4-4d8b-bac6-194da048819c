"""
Polygon.io WebSocket client for real-time market data streaming.
"""
import json
import asyncio
import websockets
import logging
from datetime import datetime
from typing import Dict, List, Callable, Optional
from src.config.config import POLYGON_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PolygonWebSocketClient:
    """
    WebSocket client for real-time Polygon.io data streaming.
    
    Features:
    - Real-time trade data
    - Real-time quote data
    - Minute aggregates
    - Custom event handlers
    """

    def __init__(self, api_key: str = POLYGON_API_KEY):
        """
        Initialize the WebSocket client.

        Args:
            api_key (str): Polygon.io API key.
        """
        self.api_key = api_key
        self.websocket = None
        self.is_connected = False
        self.subscriptions = set()
        
        # Event handlers
        self.trade_handlers: List[Callable] = []
        self.quote_handlers: List[Callable] = []
        self.minute_agg_handlers: List[Callable] = []
        self.error_handlers: List[Callable] = []
        
        # WebSocket URL for stocks
        self.ws_url = "wss://socket.polygon.io/stocks"

    async def connect(self):
        """Connect to Polygon WebSocket."""
        try:
            logger.info("Connecting to Polygon WebSocket...")
            self.websocket = await websockets.connect(self.ws_url)
            self.is_connected = True
            
            # Authenticate
            auth_message = {
                "action": "auth",
                "params": self.api_key
            }
            await self.websocket.send(json.dumps(auth_message))
            
            # Wait for auth response
            response = await self.websocket.recv()
            auth_response = json.loads(response)

            # Handle both single response and list of responses
            if isinstance(auth_response, list):
                auth_response = auth_response[0] if auth_response else {}

            # Check for successful connection (Polygon uses different status messages)
            status = auth_response.get("status")
            if status in ["auth_success", "connected", "authenticated"]:
                logger.info(f"WebSocket authentication successful: {status}")
                return True
            elif status == "connected":
                # For "connected" status, we need to wait for auth response
                logger.info("WebSocket connected, waiting for authentication...")
                try:
                    auth_response2 = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
                    auth_data = json.loads(auth_response2)
                    if isinstance(auth_data, list):
                        auth_data = auth_data[0] if auth_data else {}

                    if auth_data.get("status") == "auth_success":
                        logger.info("WebSocket authentication successful")
                        return True
                except asyncio.TimeoutError:
                    logger.warning("Authentication timeout, but connection established")
                    return True  # Assume success if connected

            logger.error(f"WebSocket authentication failed: {auth_response}")
            return False
                
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {str(e)}")
            self.is_connected = False
            return False

    async def disconnect(self):
        """Disconnect from WebSocket."""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            logger.info("WebSocket disconnected")

    async def subscribe_to_trades(self, tickers: List[str]):
        """
        Subscribe to real-time trade data for specified tickers.

        Args:
            tickers (List[str]): List of ticker symbols.
        """
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False

        subscribe_message = {
            "action": "subscribe",
            "params": f"T.{',T.'.join(tickers)}"
        }
        
        await self.websocket.send(json.dumps(subscribe_message))
        self.subscriptions.update([f"T.{ticker}" for ticker in tickers])
        logger.info(f"Subscribed to trades for: {', '.join(tickers)}")
        return True

    async def subscribe_to_quotes(self, tickers: List[str]):
        """
        Subscribe to real-time quote data for specified tickers.

        Args:
            tickers (List[str]): List of ticker symbols.
        """
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False

        subscribe_message = {
            "action": "subscribe",
            "params": f"Q.{',Q.'.join(tickers)}"
        }
        
        await self.websocket.send(json.dumps(subscribe_message))
        self.subscriptions.update([f"Q.{ticker}" for ticker in tickers])
        logger.info(f"Subscribed to quotes for: {', '.join(tickers)}")
        return True

    async def subscribe_to_minute_aggregates(self, tickers: List[str]):
        """
        Subscribe to real-time minute aggregate data for specified tickers.

        Args:
            tickers (List[str]): List of ticker symbols.
        """
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False

        subscribe_message = {
            "action": "subscribe",
            "params": f"AM.{',AM.'.join(tickers)}"
        }
        
        await self.websocket.send(json.dumps(subscribe_message))
        self.subscriptions.update([f"AM.{ticker}" for ticker in tickers])
        logger.info(f"Subscribed to minute aggregates for: {', '.join(tickers)}")
        return True

    def add_trade_handler(self, handler: Callable):
        """Add a handler for trade events."""
        self.trade_handlers.append(handler)

    def add_quote_handler(self, handler: Callable):
        """Add a handler for quote events."""
        self.quote_handlers.append(handler)

    def add_minute_agg_handler(self, handler: Callable):
        """Add a handler for minute aggregate events."""
        self.minute_agg_handlers.append(handler)

    def add_error_handler(self, handler: Callable):
        """Add a handler for error events."""
        self.error_handlers.append(handler)

    async def _handle_message(self, message: str):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(message)
            
            # Handle different message types
            if isinstance(data, list):
                for item in data:
                    await self._process_data_item(item)
            else:
                await self._process_data_item(data)
                
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {str(e)}")
            for handler in self.error_handlers:
                try:
                    await handler({"error": str(e), "message": message})
                except Exception as handler_error:
                    logger.error(f"Error in error handler: {str(handler_error)}")

    async def _process_data_item(self, item: Dict):
        """Process individual data items from WebSocket."""
        event_type = item.get("ev")
        
        if event_type == "T":  # Trade
            trade_data = {
                "ticker": item.get("sym"),
                "price": item.get("p"),
                "size": item.get("s"),
                "timestamp": item.get("t"),
                "conditions": item.get("c", []),
                "exchange": item.get("x")
            }
            
            for handler in self.trade_handlers:
                try:
                    await handler(trade_data)
                except Exception as e:
                    logger.error(f"Error in trade handler: {str(e)}")
                    
        elif event_type == "Q":  # Quote
            quote_data = {
                "ticker": item.get("sym"),
                "bid": item.get("bp"),
                "ask": item.get("ap"),
                "bid_size": item.get("bs"),
                "ask_size": item.get("as"),
                "timestamp": item.get("t"),
                "exchange": item.get("x")
            }
            
            for handler in self.quote_handlers:
                try:
                    await handler(quote_data)
                except Exception as e:
                    logger.error(f"Error in quote handler: {str(e)}")
                    
        elif event_type == "AM":  # Minute Aggregate
            minute_data = {
                "ticker": item.get("sym"),
                "open": item.get("o"),
                "high": item.get("h"),
                "low": item.get("l"),
                "close": item.get("c"),
                "volume": item.get("v"),
                "timestamp": item.get("s"),  # Start timestamp
                "end_timestamp": item.get("e"),  # End timestamp
                "vwap": item.get("vw"),
                "transactions": item.get("n")
            }
            
            for handler in self.minute_agg_handlers:
                try:
                    await handler(minute_data)
                except Exception as e:
                    logger.error(f"Error in minute aggregate handler: {str(e)}")

    async def listen(self):
        """Listen for WebSocket messages continuously."""
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return

        try:
            logger.info("Starting WebSocket message listener...")
            async for message in self.websocket:
                await self._handle_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
            self.is_connected = False
        except Exception as e:
            logger.error(f"Error in WebSocket listener: {str(e)}")
            self.is_connected = False

    async def run_forever(self):
        """Run the WebSocket client continuously with auto-reconnection."""
        while True:
            try:
                if not self.is_connected:
                    success = await self.connect()
                    if not success:
                        logger.error("Failed to connect, retrying in 30 seconds...")
                        await asyncio.sleep(30)
                        continue
                
                # Re-subscribe to previous subscriptions
                if self.subscriptions:
                    logger.info("Re-subscribing to previous subscriptions...")
                    # Group subscriptions by type
                    trades = [sub[2:] for sub in self.subscriptions if sub.startswith("T.")]
                    quotes = [sub[2:] for sub in self.subscriptions if sub.startswith("Q.")]
                    minute_aggs = [sub[3:] for sub in self.subscriptions if sub.startswith("AM.")]
                    
                    if trades:
                        await self.subscribe_to_trades(trades)
                    if quotes:
                        await self.subscribe_to_quotes(quotes)
                    if minute_aggs:
                        await self.subscribe_to_minute_aggregates(minute_aggs)
                
                # Listen for messages
                await self.listen()
                
            except Exception as e:
                logger.error(f"WebSocket error: {str(e)}")
                self.is_connected = False
                await asyncio.sleep(10)  # Wait before reconnecting
