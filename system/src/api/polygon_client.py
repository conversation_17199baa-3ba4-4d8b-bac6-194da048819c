"""
Polygon.io API client for fetching market data.
"""
import requests
import pandas as pd
from datetime import datetime
import logging
from src.utils.rate_limiter import RateLimiter
from src.config.config import POLYGON_API_KEY, POLYGON_BASE_URL, RATE_LIMIT_PER_MINUTE

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PolygonClient:
    """
    Client for interacting with the Polygon.io API.

    Attributes:
        api_key (str): The Polygon.io API key.
        base_url (str): The base URL for the Polygon.io API.
        rate_limiter (RateLimiter): Rate limiter to prevent exceeding API limits.
    """

    def __init__(self, api_key=POLYGON_API_KEY, base_url=POLYGON_BASE_URL):
        """
        Initialize the Polygon.io API client.

        Args:
            api_key (str, optional): The Polygon.io API key. Defaults to the value from config.
            base_url (str, optional): The base URL for the Polygon.io API. Defaults to the value from config.
        """
        self.api_key = api_key
        self.base_url = base_url
        self.rate_limiter = RateLimiter(RATE_LIMIT_PER_MINUTE)

        # Validate API key
        if not self.api_key:
            raise ValueError("Polygon API key is required")

    def _make_request(self, endpoint, params=None):
        """
        Make a request to the Polygon.io API with rate limiting.

        Args:
            endpoint (str): The API endpoint to request.
            params (dict, optional): Query parameters for the request. Defaults to None.

        Returns:
            dict: The JSON response from the API.

        Raises:
            requests.exceptions.RequestException: If the request fails.
        """
        # Apply rate limiting
        wait_time = self.rate_limiter.wait_if_needed()
        if wait_time > 0:
            logger.info(f"Rate limit applied - waited {wait_time:.2f} seconds")

        # Prepare request
        url = f"{self.base_url}{endpoint}"

        if params is None:
            params = {}

        # Add API key to query parameters
        params['apiKey'] = self.api_key

        try:
            logger.info(f"Making request to {url}")
            response = requests.get(url, params=params)
            response.raise_for_status()  # Raise exception for 4XX/5XX responses
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {str(e)}")

            # Check for rate limit errors
            if response.status_code == 429:
                logger.error("Rate limit exceeded. Consider reducing request frequency.")

            raise

    def get_previous_close(self, ticker):
        """
        Get the previous day's OHLC (Open, High, Low, Close) data for a ticker.

        Args:
            ticker (str): The ticker symbol.

        Returns:
            pandas.DataFrame: DataFrame containing the OHLC data.
        """
        # Make API request
        endpoint = f"/v2/aggs/ticker/{ticker}/prev"
        params = {
            "adjusted": "true"
        }

        try:
            response_data = self._make_request(endpoint, params)

            # Check if results are available
            if 'results' not in response_data or not response_data['results']:
                logger.warning(f"No data found for {ticker}")
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(response_data['results'])

            # Rename columns to more readable names
            column_mapping = {
                'v': 'volume',
                'o': 'open',
                'c': 'close',
                'h': 'high',
                'l': 'low',
                't': 'timestamp',
                'n': 'transactions',
                'T': 'ticker'
            }
            df = df.rename(columns=column_mapping)

            # Convert timestamp from milliseconds to datetime
            df['date'] = pd.to_datetime(df['timestamp'], unit='ms').dt.date

            # Reorder columns
            columns_order = ['ticker', 'date', 'open', 'high', 'low', 'close', 'volume', 'transactions', 'timestamp']
            df = df[columns_order]

            return df

        except Exception as e:
            logger.error(f"Failed to get previous close data for {ticker}: {str(e)}")
            raise

    def get_ticker_details(self, ticker):
        """
        Get detailed information about a ticker.

        Args:
            ticker (str): The ticker symbol.

        Returns:
            dict: Dictionary containing ticker details.
        """
        # Make API request
        endpoint = f"/v3/reference/tickers/{ticker}"

        try:
            response_data = self._make_request(endpoint)

            # Check if results are available
            if 'results' not in response_data:
                logger.warning(f"No details found for {ticker}")
                return {}

            return response_data['results']

        except Exception as e:
            logger.error(f"Failed to get ticker details for {ticker}: {str(e)}")
            raise

    def get_historical_data(self, ticker, start_date, end_date, timespan='day', multiplier=1):
        """
        Get historical OHLC data for a ticker over a date range.

        Args:
            ticker (str): The ticker symbol.
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.
            timespan (str): The timespan for the data (day, hour, minute).
            multiplier (int): The multiplier for the timespan.

        Returns:
            pandas.DataFrame: DataFrame containing historical OHLC data.
        """
        endpoint = f"/v2/aggs/ticker/{ticker}/range/{multiplier}/{timespan}/{start_date}/{end_date}"
        params = {
            "adjusted": "true",
            "sort": "asc",
            "limit": 50000  # Maximum allowed by Polygon
        }

        try:
            response_data = self._make_request(endpoint, params)

            # Check if results are available
            if 'results' not in response_data or not response_data['results']:
                logger.warning(f"No historical data found for {ticker} from {start_date} to {end_date}")
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(response_data['results'])

            # Rename columns to more readable names
            column_mapping = {
                'v': 'volume',
                'o': 'open',
                'c': 'close',
                'h': 'high',
                'l': 'low',
                't': 'timestamp',
                'n': 'transactions'
            }
            df = df.rename(columns=column_mapping)

            # Add ticker column
            df['ticker'] = ticker

            # Convert timestamp from milliseconds to datetime
            df['date'] = pd.to_datetime(df['timestamp'], unit='ms').dt.date
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Reorder columns
            columns_order = ['ticker', 'date', 'datetime', 'open', 'high', 'low', 'close', 'volume', 'transactions', 'timestamp']
            df = df[columns_order]

            # Sort by date
            df = df.sort_values('date').reset_index(drop=True)

            logger.info(f"Retrieved {len(df)} records for {ticker} from {start_date} to {end_date}")
            return df

        except Exception as e:
            logger.error(f"Failed to get historical data for {ticker}: {str(e)}")
            raise

    def get_market_status(self):
        """
        Get current market status.

        Returns:
            dict: Dictionary containing market status information.
        """
        endpoint = "/v1/marketstatus/now"

        try:
            response_data = self._make_request(endpoint)
            return response_data

        except Exception as e:
            logger.error(f"Failed to get market status: {str(e)}")
            raise

    def search_tickers(self, search_term=None, market_cap_gte=None, limit=100):
        """
        Search for tickers based on various criteria.

        Args:
            search_term (str, optional): Search term for ticker name or symbol.
            market_cap_gte (int, optional): Minimum market cap filter.
            limit (int): Maximum number of results to return.

        Returns:
            list: List of ticker information dictionaries.
        """
        endpoint = "/v3/reference/tickers"
        params = {
            "active": "true",
            "limit": limit,
            "sort": "market_cap",
            "order": "desc"
        }

        if search_term:
            params["search"] = search_term
        if market_cap_gte:
            params["market_cap.gte"] = market_cap_gte

        try:
            response_data = self._make_request(endpoint, params)

            # Check if results are available
            if 'results' not in response_data:
                logger.warning("No tickers found")
                return []

            return response_data['results']

        except Exception as e:
            logger.error(f"Failed to search tickers: {str(e)}")
            raise

    def get_market_snapshot(self, tickers):
        """
        Get current market snapshot for multiple tickers in one API call.
        Uses Polygon's snapshot endpoint for efficient bulk data retrieval.

        Args:
            tickers (list): List of ticker symbols to get snapshots for.

        Returns:
            dict: Dictionary with ticker symbols as keys and snapshot data as values.
        """
        if not tickers:
            return {}

        # Convert tickers to comma-separated string
        ticker_string = ','.join(tickers)
        endpoint = f"/v2/snapshot/locale/us/markets/stocks/tickers"

        params = {
            "tickers": ticker_string
        }

        try:
            logger.info(f"Getting market snapshot for {len(tickers)} tickers")
            response_data = self._make_request(endpoint, params)

            # Check if tickers data is available (Polygon uses 'tickers' not 'results')
            if 'tickers' not in response_data or not response_data['tickers']:
                logger.warning(f"No snapshot data found for tickers: {ticker_string}")
                return {}

            # Convert to dictionary format for easy lookup
            snapshot_dict = {}
            for result in response_data['tickers']:
                ticker = result.get('ticker')
                if ticker:
                    snapshot_dict[ticker] = {
                        'ticker': ticker,
                        'current_price': result.get('day', {}).get('c'),  # Current/close price
                        'open': result.get('day', {}).get('o'),
                        'high': result.get('day', {}).get('h'),
                        'low': result.get('day', {}).get('l'),
                        'volume': result.get('day', {}).get('v'),
                        'previous_close': result.get('prevDay', {}).get('c'),
                        'change': result.get('day', {}).get('c', 0) - result.get('prevDay', {}).get('c', 0),
                        'change_percent': ((result.get('day', {}).get('c', 0) - result.get('prevDay', {}).get('c', 0)) /
                                         result.get('prevDay', {}).get('c', 1)) * 100 if result.get('prevDay', {}).get('c') else 0,
                        'timestamp': datetime.now().isoformat()
                    }

            logger.info(f"Successfully retrieved snapshots for {len(snapshot_dict)} tickers")
            return snapshot_dict

        except Exception as e:
            logger.error(f"Failed to get market snapshot: {str(e)}")
            raise

    def get_real_time_quote(self, ticker):
        """
        Get real-time quote for a single ticker.
        Uses Polygon's real-time quote endpoint.

        Args:
            ticker (str): The ticker symbol.

        Returns:
            dict: Real-time quote data.
        """
        endpoint = f"/v1/last_quote/stocks/{ticker}"

        try:
            response_data = self._make_request(endpoint)

            # Check if results are available
            if 'results' not in response_data:
                logger.warning(f"No real-time quote found for {ticker}")
                return {}

            result = response_data['results']
            return {
                'ticker': ticker,
                'bid': result.get('P'),  # Bid price
                'ask': result.get('p'),  # Ask price
                'bid_size': result.get('S'),  # Bid size
                'ask_size': result.get('s'),  # Ask size
                'timestamp': result.get('t'),
                'timeframe': result.get('f'),
                'exchange': result.get('x')
            }

        except Exception as e:
            logger.error(f"Failed to get real-time quote for {ticker}: {str(e)}")
            raise
