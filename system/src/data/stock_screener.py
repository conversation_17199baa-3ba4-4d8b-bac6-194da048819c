"""
Stock screening system to identify top candidates for swing trading analysis.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import logging
from src.api.polygon_client import PolygonClient
from src.data.data_manager import DataManager

logger = logging.getLogger(__name__)

class StockScreener:
    """
    Screens stocks based on volume, momentum, and fundamental criteria.
    """

    def __init__(self, data_manager: DataManager = None):
        """
        Initialize the stock screener.

        Args:
            data_manager (DataManager): Data manager instance for data retrieval.
        """
        self.client = PolygonClient()
        self.data_manager = data_manager or DataManager()

    def get_high_volume_stocks(self, min_dollar_volume: float = 50_000_000,
                              limit: int = 500) -> List[Dict]:
        """
        Get stocks with high average daily dollar volume.

        Args:
            min_dollar_volume (float): Minimum daily dollar volume threshold.
            limit (int): Maximum number of stocks to return.

        Returns:
            List[Dict]: List of stock information dictionaries.
        """
        logger.info(f"Screening for stocks with min ${min_dollar_volume:,.0f} daily volume")

        # Use predefined list of high-volume stocks for demo
        # In production, this would use the API search functionality
        top_stocks = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK.B',
            'UNH', 'JNJ', 'V', 'PG', 'JPM', 'HD', 'MA', 'ABBV', 'PFE', 'KO',
            'AVGO', 'PEP', 'TMO', 'COST', 'WMT', 'DIS', 'ABT', 'CRM', 'VZ',
            'ADBE', 'NFLX', 'NKE', 'XOM', 'CMCSA', 'DHR', 'LIN', 'ORCL',
            'ACN', 'TXN', 'CVX', 'QCOM', 'NEE', 'PM', 'HON', 'RTX', 'UNP',
            'LOW', 'SPGI', 'INTU', 'IBM', 'GS', 'CAT', 'AMD', 'AMGN', 'ISRG',
            'BKNG', 'AXP', 'T', 'SYK', 'TJX', 'BLK', 'MDLZ', 'GILD', 'MU',
            'CVS', 'VRTX', 'LRCX', 'ADI', 'TMUS', 'REGN', 'C', 'PLD', 'ZTS',
            'MMM', 'PYPL', 'SCHW', 'CB', 'SO', 'FIS', 'MO', 'BSX', 'DUK',
            'CL', 'ITW', 'EQIX', 'AON', 'APD', 'CSX', 'ICE', 'WM', 'GD',
            'ATVI', 'USB', 'COP', 'EMR', 'PNC', 'NSC', 'SHW', 'KLAC', 'FCX',
            'GM', 'F', 'INTC', 'AMAT', 'MRNA', 'ROKU', 'SQ', 'SHOP', 'UBER',
            'LYFT', 'SNAP', 'TWTR', 'ZM', 'PTON', 'DOCU', 'CRM'
        ]

        # Limit to requested number
        selected_tickers = top_stocks[:min(limit, len(top_stocks))]

        # Convert to expected format
        ticker_data = []
        for ticker in selected_tickers:
            ticker_data.append({
                'ticker': ticker,
                'name': f'{ticker} Inc.',  # Placeholder
                'market_cap': 1_000_000_000,  # Placeholder
                'primary_exchange': 'NASDAQ'  # Placeholder
            })

        logger.info(f"Selected {len(ticker_data)} candidate stocks from predefined list")
        return ticker_data

    def calculate_momentum_metrics(self, df: pd.DataFrame, periods: List[int] = [20, 50]) -> Dict:
        """
        Calculate momentum metrics for a stock.

        Args:
            df (pd.DataFrame): Historical price data.
            periods (List[int]): Periods for rate of change calculation.

        Returns:
            Dict: Momentum metrics.
        """
        if df.empty or len(df) < max(periods):
            return {}

        metrics = {}

        for period in periods:
            if len(df) >= period:
                # Rate of change
                roc = ((df['close'].iloc[-1] / df['close'].iloc[-period]) - 1) * 100
                metrics[f'roc_{period}d'] = roc

                # Average volume
                metrics[f'avg_volume_{period}d'] = df['volume'].tail(period).mean()

                # Average dollar volume
                avg_dollar_vol = (df['close'] * df['volume']).tail(period).mean()
                metrics[f'avg_dollar_volume_{period}d'] = avg_dollar_vol

        # Current price and volume
        metrics['current_price'] = df['close'].iloc[-1]
        metrics['current_volume'] = df['volume'].iloc[-1]

        # Relative volume (current vs 20-day average)
        if len(df) >= 20:
            avg_vol_20d = df['volume'].tail(20).mean()
            metrics['relative_volume'] = df['volume'].iloc[-1] / avg_vol_20d

        return metrics

    def screen_top_momentum_stocks(self, candidate_tickers: List[str],
                                 start_date: str, end_date: str,
                                 top_n: int = 100) -> List[Dict]:
        """
        Screen and rank stocks by momentum metrics.

        Args:
            candidate_tickers (List[str]): List of ticker symbols to analyze.
            start_date (str): Start date for historical data.
            end_date (str): End date for historical data.
            top_n (int): Number of top stocks to return.

        Returns:
            List[Dict]: Top momentum stocks with metrics.
        """
        logger.info(f"Analyzing momentum for {len(candidate_tickers)} stocks")

        stock_metrics = []

        for ticker in candidate_tickers:
            try:
                # Get historical data
                df = self.data_manager.get_historical_data(ticker, start_date, end_date)

                if df.empty:
                    logger.warning(f"No data for {ticker}")
                    continue

                # Calculate momentum metrics
                metrics = self.calculate_momentum_metrics(df)

                if not metrics:
                    continue

                # Add ticker info
                metrics['ticker'] = ticker

                # Filter by minimum dollar volume
                if metrics.get('avg_dollar_volume_20d', 0) >= 50_000_000:
                    stock_metrics.append(metrics)

            except Exception as e:
                logger.error(f"Failed to analyze {ticker}: {e}")
                continue

        # Sort by 20-day rate of change (momentum)
        stock_metrics.sort(key=lambda x: x.get('roc_20d', -999), reverse=True)

        # Return top N stocks
        top_stocks = stock_metrics[:top_n]

        logger.info(f"Selected top {len(top_stocks)} momentum stocks")
        return top_stocks

    def get_top_swing_candidates(self, lookback_days: int = 730,
                               top_n: int = 100) -> Tuple[List[str], pd.DataFrame]:
        """
        Get the top swing trading candidates based on volume and momentum.

        Args:
            lookback_days (int): Number of days to look back for analysis.
            top_n (int): Number of top candidates to return.

        Returns:
            Tuple[List[str], pd.DataFrame]: List of ticker symbols and summary DataFrame.
        """
        # Calculate date range
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=lookback_days)).strftime('%Y-%m-%d')

        logger.info(f"Screening swing trading candidates from {start_date} to {end_date}")

        # Step 1: Get high volume stocks
        high_volume_stocks = self.get_high_volume_stocks()

        if not high_volume_stocks:
            logger.error("No high volume stocks found")
            return [], pd.DataFrame()

        # Extract ticker symbols
        candidate_tickers = [stock['ticker'] for stock in high_volume_stocks if 'ticker' in stock]

        # Step 2: Analyze momentum
        top_momentum_stocks = self.screen_top_momentum_stocks(
            candidate_tickers, start_date, end_date, top_n
        )

        if not top_momentum_stocks:
            logger.error("No momentum stocks found")
            return [], pd.DataFrame()

        # Create summary DataFrame
        summary_df = pd.DataFrame(top_momentum_stocks)

        # Extract ticker list
        top_tickers = summary_df['ticker'].tolist()

        logger.info(f"Final selection: {len(top_tickers)} swing trading candidates")

        return top_tickers, summary_df

    def add_fundamental_filters(self, tickers: List[str]) -> List[Dict]:
        """
        Add fundamental data filters to the stock list.

        Args:
            tickers (List[str]): List of ticker symbols.

        Returns:
            List[Dict]: Enhanced stock information with fundamental data.
        """
        enhanced_stocks = []

        for ticker in tickers:
            try:
                # Get ticker details from Polygon
                details = self.client.get_ticker_details(ticker)

                if details:
                    stock_info = {
                        'ticker': ticker,
                        'name': details.get('name', ''),
                        'market_cap': details.get('market_cap', 0),
                        'sector': details.get('sic_description', ''),
                        'exchange': details.get('primary_exchange', ''),
                        'currency': details.get('currency_name', 'USD')
                    }
                    enhanced_stocks.append(stock_info)

            except Exception as e:
                logger.error(f"Failed to get fundamental data for {ticker}: {e}")
                continue

        return enhanced_stocks
