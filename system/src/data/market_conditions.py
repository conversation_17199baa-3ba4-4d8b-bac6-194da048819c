"""
Market condition analysis using SPY as the primary benchmark.
Defines bullish/bearish market periods for swing trading analysis.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging
from src.data.data_manager import DataManager

logger = logging.getLogger(__name__)

class MarketConditionAnalyzer:
    """
    Enhanced market condition analyzer using SPY (S&P 500 ETF) as the benchmark.
    Classifies market into three regimes: Bullish, Bearish, and Neutral.
    """

    def __init__(self, data_manager: DataManager = None):
        """
        Initialize the market condition analyzer.

        Args:
            data_manager (DataManager): Data manager instance for data retrieval.
        """
        self.data_manager = data_manager or DataManager()

        # Market regime classification thresholds
        self.regime_config = {
            'bullish_threshold': 4,      # Minimum score for bullish regime
            'bearish_threshold': 2,      # Maximum score for bearish regime
            'persistence_days': 3,       # Days to confirm regime change
            'volatility_threshold': 0.02 # Daily volatility threshold
        }
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for market condition analysis.
        
        Args:
            df (pd.DataFrame): SPY historical data.
        
        Returns:
            pd.DataFrame: DataFrame with technical indicators added.
        """
        df = df.copy()
        
        # Moving averages
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['sma_200'] = df['close'].rolling(window=200).mean()
        
        # Exponential moving averages
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # RSI (Relative Strength Index)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # Volume indicators
        df['volume_sma_20'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_20']
        
        # Price position within daily range
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        return df
    
    def define_bullish_conditions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Define bullish market conditions based on multiple criteria.
        
        Args:
            df (pd.DataFrame): SPY data with technical indicators.
        
        Returns:
            pd.DataFrame: DataFrame with bullish condition flags.
        """
        df = df.copy()
        
        # Individual bullish criteria
        df['bull_ma_trend'] = (df['close'] > df['sma_50']) & (df['sma_50'] > df['sma_200'])
        df['bull_short_ma'] = df['close'] > df['sma_20']
        df['bull_rsi'] = (df['rsi'] > 40) & (df['rsi'] < 80)
        df['bull_macd'] = df['macd'] > df['macd_signal']
        df['bull_price_action'] = df['close'] > df['close'].shift(1)
        df['bull_volume'] = df['volume_ratio'] > 0.8
        
        # Composite bullish score (0-6)
        bullish_criteria = [
            'bull_ma_trend', 'bull_short_ma', 'bull_rsi', 
            'bull_macd', 'bull_price_action', 'bull_volume'
        ]
        df['bullish_score'] = df[bullish_criteria].sum(axis=1)
        
        # Define bullish periods (score >= 4 out of 6)
        df['is_bullish'] = df['bullish_score'] >= 4
        
        # Smooth bullish periods (require 3+ consecutive days)
        df['bullish_smooth'] = df['is_bullish'].rolling(window=3).sum() >= 2
        
        return df
    
    def get_market_condition_periods(self, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get market condition analysis for a date range.
        
        Args:
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.
        
        Returns:
            pd.DataFrame: SPY data with market condition analysis.
        """
        logger.info(f"Analyzing market conditions from {start_date} to {end_date}")
        
        # Get SPY data
        spy_data = self.data_manager.get_spy_data(start_date, end_date)
        
        if spy_data.empty:
            logger.error("No SPY data available for market condition analysis")
            return pd.DataFrame()
        
        # Calculate technical indicators
        spy_data = self.calculate_technical_indicators(spy_data)
        
        # Define bullish conditions
        spy_data = self.define_bullish_conditions(spy_data)
        
        logger.info(f"Market condition analysis complete for {len(spy_data)} trading days")
        
        return spy_data
    
    def get_bullish_periods(self, market_data: pd.DataFrame) -> List[Tuple[str, str]]:
        """
        Extract continuous bullish periods from market data.
        
        Args:
            market_data (pd.DataFrame): Market data with bullish flags.
        
        Returns:
            List[Tuple[str, str]]: List of (start_date, end_date) tuples for bullish periods.
        """
        if market_data.empty or 'bullish_smooth' not in market_data.columns:
            return []
        
        bullish_periods = []
        in_bullish_period = False
        start_date = None
        
        for idx, row in market_data.iterrows():
            if row['bullish_smooth'] and not in_bullish_period:
                # Start of bullish period
                start_date = row['date']
                in_bullish_period = True
            elif not row['bullish_smooth'] and in_bullish_period:
                # End of bullish period
                end_date = market_data.iloc[idx-1]['date']
                bullish_periods.append((str(start_date), str(end_date)))
                in_bullish_period = False
        
        # Handle case where data ends during a bullish period
        if in_bullish_period and start_date:
            end_date = market_data.iloc[-1]['date']
            bullish_periods.append((str(start_date), str(end_date)))
        
        logger.info(f"Identified {len(bullish_periods)} bullish periods")
        return bullish_periods
    
    def get_market_summary(self, market_data: pd.DataFrame) -> Dict:
        """
        Get summary statistics for market conditions.
        
        Args:
            market_data (pd.DataFrame): Market data with condition analysis.
        
        Returns:
            Dict: Market condition summary.
        """
        if market_data.empty:
            return {}
        
        total_days = len(market_data)
        bullish_days = market_data['bullish_smooth'].sum()
        
        summary = {
            'total_trading_days': total_days,
            'bullish_days': int(bullish_days),
            'bearish_days': total_days - int(bullish_days),
            'bullish_percentage': (bullish_days / total_days) * 100,
            'avg_bullish_score': market_data['bullish_score'].mean(),
            'current_condition': 'Bullish' if market_data['bullish_smooth'].iloc[-1] else 'Bearish',
            'spy_performance': {
                'start_price': market_data['close'].iloc[0],
                'end_price': market_data['close'].iloc[-1],
                'total_return': ((market_data['close'].iloc[-1] / market_data['close'].iloc[0]) - 1) * 100
            }
        }
        
        return summary

    def classify_market_regime(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """
        Classify market into three regimes: Bullish, Bearish, Neutral.

        Args:
            market_data (pd.DataFrame): Market data with technical indicators.

        Returns:
            pd.DataFrame: Market data with regime classifications.
        """
        df = market_data.copy()

        # Enhanced regime scoring (0-8 scale)
        df['regime_score'] = 0

        # Trend indicators (0-3 points)
        df['regime_score'] += np.where(df['close'] > df['sma_50'], 1, 0)
        df['regime_score'] += np.where(df['sma_50'] > df['sma_200'], 1, 0)
        df['regime_score'] += np.where(df['close'] > df['sma_20'], 1, 0)

        # Momentum indicators (0-2 points)
        df['regime_score'] += np.where(df['macd'] > df['macd_signal'], 1, 0)
        df['regime_score'] += np.where((df['rsi'] > 45) & (df['rsi'] < 75), 1, 0)

        # Volume and volatility (0-2 points)
        df['regime_score'] += np.where(df['volume_ratio'] > 0.9, 1, 0)
        df['daily_return'] = df['close'].pct_change()
        df['volatility_5d'] = df['daily_return'].rolling(5).std()
        df['regime_score'] += np.where(df['volatility_5d'] < self.regime_config['volatility_threshold'], 1, 0)

        # Price action (0-1 point)
        df['regime_score'] += np.where(df['close'] > df['close'].shift(1), 1, 0)

        # Initial regime classification
        df['regime_raw'] = 'Neutral'
        df.loc[df['regime_score'] >= self.regime_config['bullish_threshold'], 'regime_raw'] = 'Bullish'
        df.loc[df['regime_score'] <= self.regime_config['bearish_threshold'], 'regime_raw'] = 'Bearish'

        # Apply persistence filter to reduce whipsaws
        df['regime'] = self._apply_regime_persistence(df['regime_raw'])

        # Add regime transition signals
        df['regime_change'] = df['regime'] != df['regime'].shift(1)
        df['regime_duration'] = df.groupby((df['regime'] != df['regime'].shift(1)).cumsum()).cumcount() + 1

        return df

    def _apply_regime_persistence(self, regime_series: pd.Series) -> pd.Series:
        """
        Apply persistence filter to reduce regime switching noise.

        Args:
            regime_series (pd.Series): Raw regime classifications.

        Returns:
            pd.Series: Smoothed regime classifications.
        """
        smoothed = regime_series.copy()
        persistence_days = self.regime_config['persistence_days']

        for i in range(persistence_days, len(regime_series)):
            # Check if regime has been consistent for persistence_days
            recent_regimes = regime_series.iloc[i-persistence_days:i+1]

            if len(recent_regimes.unique()) == 1:
                # Regime has been consistent, keep it
                smoothed.iloc[i] = recent_regimes.iloc[-1]
            else:
                # Mixed signals, keep previous smoothed regime
                smoothed.iloc[i] = smoothed.iloc[i-1]

        return smoothed

    def get_regime_periods(self, market_data: pd.DataFrame) -> Dict[str, List[Tuple[str, str]]]:
        """
        Extract continuous periods for each market regime.

        Args:
            market_data (pd.DataFrame): Market data with regime classifications.

        Returns:
            Dict[str, List[Tuple[str, str]]]: Regime periods by type.
        """
        if market_data.empty or 'regime' not in market_data.columns:
            return {'Bullish': [], 'Bearish': [], 'Neutral': []}

        regime_periods = {'Bullish': [], 'Bearish': [], 'Neutral': []}

        current_regime = None
        start_date = None

        for idx, row in market_data.iterrows():
            regime = row['regime']
            date = str(row['date'])

            if regime != current_regime:
                # Regime change detected
                if current_regime is not None and start_date is not None:
                    # End previous regime period
                    end_date = market_data.iloc[idx-1]['date']
                    regime_periods[current_regime].append((start_date, str(end_date)))

                # Start new regime period
                current_regime = regime
                start_date = date

        # Handle the last period
        if current_regime is not None and start_date is not None:
            end_date = str(market_data.iloc[-1]['date'])
            regime_periods[current_regime].append((start_date, end_date))

        return regime_periods

    def get_regime_statistics(self, market_data: pd.DataFrame) -> Dict:
        """
        Calculate comprehensive regime statistics.

        Args:
            market_data (pd.DataFrame): Market data with regime classifications.

        Returns:
            Dict: Regime statistics and analysis.
        """
        if market_data.empty or 'regime' not in market_data.columns:
            return {}

        total_days = len(market_data)
        regime_counts = market_data['regime'].value_counts()

        # Calculate regime percentages
        regime_percentages = {
            'Bullish': (regime_counts.get('Bullish', 0) / total_days) * 100,
            'Bearish': (regime_counts.get('Bearish', 0) / total_days) * 100,
            'Neutral': (regime_counts.get('Neutral', 0) / total_days) * 100
        }

        # Calculate average regime duration
        regime_durations = {}
        for regime in ['Bullish', 'Bearish', 'Neutral']:
            regime_data = market_data[market_data['regime'] == regime]
            if not regime_data.empty:
                avg_duration = regime_data['regime_duration'].mean()
                max_duration = regime_data['regime_duration'].max()
                regime_durations[regime] = {
                    'avg_duration': avg_duration,
                    'max_duration': max_duration
                }

        # Calculate performance by regime
        regime_performance = {}
        for regime in ['Bullish', 'Bearish', 'Neutral']:
            regime_data = market_data[market_data['regime'] == regime]
            if not regime_data.empty and 'daily_return' in regime_data.columns:
                total_return = ((1 + regime_data['daily_return']).prod() - 1) * 100
                avg_daily_return = regime_data['daily_return'].mean() * 100
                volatility = regime_data['daily_return'].std() * 100
                regime_performance[regime] = {
                    'total_return': total_return,
                    'avg_daily_return': avg_daily_return,
                    'volatility': volatility,
                    'sharpe_ratio': avg_daily_return / volatility if volatility > 0 else 0
                }

        current_regime = market_data['regime'].iloc[-1] if not market_data.empty else 'Unknown'
        current_duration = market_data['regime_duration'].iloc[-1] if not market_data.empty else 0

        return {
            'total_trading_days': total_days,
            'regime_percentages': regime_percentages,
            'regime_durations': regime_durations,
            'regime_performance': regime_performance,
            'current_regime': current_regime,
            'current_regime_duration': current_duration,
            'regime_transitions': market_data['regime_change'].sum(),
            'avg_regime_score': market_data['regime_score'].mean()
        }

    def filter_data_by_market_condition(self, stock_data: pd.DataFrame,
                                      market_data: pd.DataFrame,
                                      bullish_only: bool = True) -> pd.DataFrame:
        """
        Filter stock data to include only specific market conditions.
        
        Args:
            stock_data (pd.DataFrame): Stock price data.
            market_data (pd.DataFrame): Market condition data.
            bullish_only (bool): If True, filter for bullish periods only.
        
        Returns:
            pd.DataFrame: Filtered stock data.
        """
        if stock_data.empty or market_data.empty:
            return stock_data
        
        # Merge on date
        merged = stock_data.merge(
            market_data[['date', 'bullish_smooth']], 
            on='date', 
            how='left'
        )
        
        if bullish_only:
            filtered = merged[merged['bullish_smooth'] == True].copy()
        else:
            filtered = merged[merged['bullish_smooth'] == False].copy()
        
        # Remove the market condition column
        if 'bullish_smooth' in filtered.columns:
            filtered = filtered.drop('bullish_smooth', axis=1)

        return filtered

    def filter_data_by_regime(self, stock_data: pd.DataFrame,
                             market_data: pd.DataFrame,
                             regime: str = 'Bullish') -> pd.DataFrame:
        """
        Filter stock data to include only specific market regime periods.

        Args:
            stock_data (pd.DataFrame): Stock price data.
            market_data (pd.DataFrame): Market condition data with regime classifications.
            regime (str): Target regime ('Bullish', 'Bearish', or 'Neutral').

        Returns:
            pd.DataFrame: Filtered stock data.
        """
        if stock_data.empty or market_data.empty:
            return stock_data

        if 'regime' not in market_data.columns:
            logger.warning("Market data does not contain regime classifications")
            return stock_data

        # Merge on date
        merged = stock_data.merge(
            market_data[['date', 'regime']],
            on='date',
            how='left'
        )

        # Filter for specific regime
        filtered = merged[merged['regime'] == regime].copy()

        # Drop the regime column before returning
        if 'regime' in filtered.columns:
            filtered = filtered.drop('regime', axis=1)

        logger.info(f"Filtered data for {regime} regime: {len(filtered)} days from {len(stock_data)} total days")

        return filtered
