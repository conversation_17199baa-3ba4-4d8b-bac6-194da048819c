"""
Data management system for swing trading analysis.
Handles data storage, retrieval, and caching.
"""
import os
import pandas as pd
import pickle
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Optional
from src.api.polygon_client import PolygonClient

logger = logging.getLogger(__name__)

class DataManager:
    """
    Manages historical data storage and retrieval for swing trading analysis.
    """

    def __init__(self, data_dir: str = "data", cache_enabled: bool = False):
        """
        Initialize the data manager.

        Args:
            data_dir (str): Directory to store cached data files.
            cache_enabled (bool): Whether to enable local caching (DEFAULT: False for live trading).
        """
        self.data_dir = data_dir
        self.cache_enabled = cache_enabled
        self.client = PolygonClient()

        # Create data directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Log caching status for transparency
        if cache_enabled:
            logger.info("Data caching ENABLED - may use stale data")
        else:
            logger.info("Data caching DISABLED - always fetching fresh data from API")

    def _get_cache_path(self, ticker: str, start_date: str, end_date: str) -> str:
        """Get the cache file path for a ticker and date range."""
        filename = f"{ticker}_{start_date}_{end_date}.pkl"
        return os.path.join(self.data_dir, filename)

    def _is_cache_valid(self, cache_path: str, max_age_hours: int = 24) -> bool:
        """Check if cached data is still valid."""
        if not os.path.exists(cache_path):
            return False

        file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(cache_path))
        return file_age < timedelta(hours=max_age_hours)

    def get_historical_data(self, ticker: str, start_date: str, end_date: str,
                          use_cache: bool = False) -> pd.DataFrame:
        """
        Get historical OHLC data for a ticker.

        Args:
            ticker (str): The ticker symbol.
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.
            use_cache (bool): Whether to use cached data if available (DEFAULT: False for live trading).

        Returns:
            pandas.DataFrame: Historical OHLC data.
        """
        cache_path = self._get_cache_path(ticker, start_date, end_date)

        # Try to load from cache first (only if both use_cache and cache_enabled are True)
        if use_cache and self.cache_enabled and self._is_cache_valid(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    df = pickle.load(f)
                logger.info(f"Loaded {ticker} data from cache (stale data warning)")
                return df
            except Exception as e:
                logger.warning(f"Failed to load cache for {ticker}: {e}")

        # Fetch fresh data from API
        logger.info(f"Fetching FRESH {ticker} data from API for {start_date} to {end_date}")
        df = self.client.get_historical_data(ticker, start_date, end_date)

        # Cache the data only if caching is enabled
        if self.cache_enabled and not df.empty:
            try:
                with open(cache_path, 'wb') as f:
                    pickle.dump(df, f)
                logger.info(f"Cached {ticker} data")
            except Exception as e:
                logger.warning(f"Failed to cache {ticker} data: {e}")

        return df

    def get_multiple_tickers_data(self, tickers: List[str], start_date: str,
                                end_date: str, use_cache: bool = False) -> Dict[str, pd.DataFrame]:
        """
        Get historical data for multiple tickers.

        Args:
            tickers (List[str]): List of ticker symbols.
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.
            use_cache (bool): Whether to use cached data if available.

        Returns:
            Dict[str, pd.DataFrame]: Dictionary mapping tickers to their data.
        """
        data = {}

        for ticker in tickers:
            try:
                df = self.get_historical_data(ticker, start_date, end_date, use_cache)
                if not df.empty:
                    data[ticker] = df
                else:
                    logger.warning(f"No data retrieved for {ticker}")
            except Exception as e:
                logger.error(f"Failed to get data for {ticker}: {e}")

        logger.info(f"Successfully retrieved data for {len(data)}/{len(tickers)} tickers")
        return data

    def get_spy_data(self, start_date: str, end_date: str, use_cache: bool = False) -> pd.DataFrame:
        """
        Get SPY (S&P 500 ETF) data for market condition analysis.

        Args:
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.
            use_cache (bool): Whether to use cached data if available.

        Returns:
            pandas.DataFrame: SPY historical data.
        """
        return self.get_historical_data('SPY', start_date, end_date, use_cache)

    def clear_cache(self, ticker: Optional[str] = None):
        """
        Clear cached data files.

        Args:
            ticker (Optional[str]): If provided, only clear cache for this ticker.
                                  If None, clear all cache files.
        """
        if ticker:
            # Clear cache for specific ticker
            for filename in os.listdir(self.data_dir):
                if filename.startswith(f"{ticker}_") and filename.endswith('.pkl'):
                    file_path = os.path.join(self.data_dir, filename)
                    os.remove(file_path)
                    logger.info(f"Removed cache file: {filename}")
        else:
            # Clear all cache files
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.pkl'):
                    file_path = os.path.join(self.data_dir, filename)
                    os.remove(file_path)
                    logger.info(f"Removed cache file: {filename}")

    def get_data_summary(self, ticker: str, df: pd.DataFrame) -> Dict:
        """
        Get summary statistics for a ticker's data.

        Args:
            ticker (str): The ticker symbol.
            df (pd.DataFrame): The ticker's historical data.

        Returns:
            Dict: Summary statistics.
        """
        if df.empty:
            return {}

        return {
            'ticker': ticker,
            'start_date': df['date'].min(),
            'end_date': df['date'].max(),
            'total_days': len(df),
            'avg_volume': df['volume'].mean(),
            'avg_dollar_volume': (df['close'] * df['volume']).mean(),
            'price_range': {
                'min': df['low'].min(),
                'max': df['high'].max(),
                'current': df['close'].iloc[-1]
            },
            'volatility': df['close'].pct_change().std() * (252 ** 0.5)  # Annualized
        }
