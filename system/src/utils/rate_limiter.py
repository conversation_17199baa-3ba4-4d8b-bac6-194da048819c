"""
Rate limiter utility to prevent exceeding API rate limits.
"""
import time
from collections import deque
from datetime import datetime, timedelta

class RateLimiter:
    """
    A rate limiter class that enforces a maximum number of requests per time period.
    
    Attributes:
        requests_per_period (int): Maximum number of requests allowed in the time period.
        period_seconds (int): The time period in seconds.
        request_timestamps (deque): A queue of timestamps for recent requests.
    """
    
    def __init__(self, requests_per_period, period_seconds=60):
        """
        Initialize the rate limiter.
        
        Args:
            requests_per_period (int): Maximum number of requests allowed in the time period.
            period_seconds (int, optional): The time period in seconds. Defaults to 60 (1 minute).
        """
        self.requests_per_period = requests_per_period
        self.period_seconds = period_seconds
        self.request_timestamps = deque(maxlen=requests_per_period)
    
    def wait_if_needed(self):
        """
        Check if a new request would exceed the rate limit and wait if necessary.
        
        Returns:
            float: The time waited in seconds, if any.
        """
        now = datetime.now()
        
        # If we haven't reached the maximum number of requests yet, no need to wait
        if len(self.request_timestamps) < self.requests_per_period:
            self.request_timestamps.append(now)
            return 0
        
        # Check if the oldest request is outside the time period
        oldest_timestamp = self.request_timestamps[0]
        time_since_oldest = (now - oldest_timestamp).total_seconds()
        
        if time_since_oldest < self.period_seconds:
            # We need to wait until the oldest request is outside the time period
            wait_time = self.period_seconds - time_since_oldest
            time.sleep(wait_time)
            self.request_timestamps.popleft()  # Remove the oldest timestamp
            self.request_timestamps.append(datetime.now())  # Add the current timestamp
            return wait_time
        else:
            # The oldest request is already outside the time period
            self.request_timestamps.popleft()  # Remove the oldest timestamp
            self.request_timestamps.append(now)  # Add the current timestamp
            return 0
