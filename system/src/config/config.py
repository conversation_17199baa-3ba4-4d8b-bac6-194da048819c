"""
Configuration module for the market analysis tool.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# Polygon.io API configuration
POLYGON_API_KEY = os.getenv('POLYGON_API_KEY', '********************************')
POLYGON_BASE_URL = 'https://api.polygon.io'

# Rate limiting configuration - Stocks Starter Plan
RATE_LIMIT_PER_MINUTE = 1000  # Unlimited API calls for Stocks Starter plan
HISTORICAL_DATA_YEARS = 5  # 5 years of historical data available
REAL_TIME_DELAY_MINUTES = 15  # 15-minute delayed data
