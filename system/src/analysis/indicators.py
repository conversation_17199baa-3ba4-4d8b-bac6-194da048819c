"""
Technical indicators for swing trading analysis.
"""
import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """
    Collection of technical indicators for swing trading analysis.
    """
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average."""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average."""
        return data.ewm(span=period).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """
        Relative Strength Index.
        
        Args:
            data (pd.Series): Price data (typically close prices).
            period (int): RSI period (default 14).
        
        Returns:
            pd.Series: RSI values.
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """
        MACD (Moving Average Convergence Divergence).
        
        Args:
            data (pd.Series): Price data.
            fast (int): Fast EMA period.
            slow (int): Slow EMA period.
            signal (int): Signal line EMA period.
        
        Returns:
            Dict[str, pd.Series]: MACD line, signal line, and histogram.
        """
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                  k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        Stochastic Oscillator.
        
        Args:
            high (pd.Series): High prices.
            low (pd.Series): Low prices.
            close (pd.Series): Close prices.
            k_period (int): %K period.
            d_period (int): %D period.
        
        Returns:
            Dict[str, pd.Series]: %K and %D values.
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            'k_percent': k_percent,
            'd_percent': d_percent
        }
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """
        Bollinger Bands.
        
        Args:
            data (pd.Series): Price data.
            period (int): Moving average period.
            std_dev (float): Standard deviation multiplier.
        
        Returns:
            Dict[str, pd.Series]: Upper, middle, and lower bands.
        """
        middle = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Average True Range.
        
        Args:
            high (pd.Series): High prices.
            low (pd.Series): Low prices.
            close (pd.Series): Close prices.
            period (int): ATR period.
        
        Returns:
            pd.Series: ATR values.
        """
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        return atr
    
    @staticmethod
    def volume_sma(volume: pd.Series, period: int = 20) -> pd.Series:
        """Volume Simple Moving Average."""
        return volume.rolling(window=period).mean()
    
    @staticmethod
    def on_balance_volume(close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        On Balance Volume.
        
        Args:
            close (pd.Series): Close prices.
            volume (pd.Series): Volume data.
        
        Returns:
            pd.Series: OBV values.
        """
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    @staticmethod
    def add_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """
        Add all technical indicators to a DataFrame.
        
        Args:
            df (pd.DataFrame): OHLCV data.
        
        Returns:
            pd.DataFrame: DataFrame with all indicators added.
        """
        df = df.copy()
        
        # Moving averages
        df['sma_10'] = TechnicalIndicators.sma(df['close'], 10)
        df['sma_20'] = TechnicalIndicators.sma(df['close'], 20)
        df['sma_50'] = TechnicalIndicators.sma(df['close'], 50)
        df['sma_200'] = TechnicalIndicators.sma(df['close'], 200)
        
        df['ema_12'] = TechnicalIndicators.ema(df['close'], 12)
        df['ema_20'] = TechnicalIndicators.ema(df['close'], 20)
        df['ema_26'] = TechnicalIndicators.ema(df['close'], 26)
        df['ema_50'] = TechnicalIndicators.ema(df['close'], 50)
        
        # RSI
        df['rsi'] = TechnicalIndicators.rsi(df['close'])
        
        # MACD
        macd_data = TechnicalIndicators.macd(df['close'])
        df['macd'] = macd_data['macd']
        df['macd_signal'] = macd_data['signal']
        df['macd_histogram'] = macd_data['histogram']
        
        # Stochastic
        stoch_data = TechnicalIndicators.stochastic(df['high'], df['low'], df['close'])
        df['stoch_k'] = stoch_data['k_percent']
        df['stoch_d'] = stoch_data['d_percent']
        
        # Bollinger Bands
        bb_data = TechnicalIndicators.bollinger_bands(df['close'])
        df['bb_upper'] = bb_data['upper']
        df['bb_middle'] = bb_data['middle']
        df['bb_lower'] = bb_data['lower']
        
        # ATR
        df['atr'] = TechnicalIndicators.atr(df['high'], df['low'], df['close'])
        
        # Volume indicators
        df['volume_sma'] = TechnicalIndicators.volume_sma(df['volume'])
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        df['obv'] = TechnicalIndicators.on_balance_volume(df['close'], df['volume'])
        
        # Price-based indicators
        df['price_change'] = df['close'].pct_change()
        df['high_low_pct'] = (df['high'] - df['low']) / df['close']
        df['close_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        return df
    
    @staticmethod
    def get_signal_summary(df: pd.DataFrame, index: int = -1) -> Dict:
        """
        Get a summary of technical signals for a specific row.
        
        Args:
            df (pd.DataFrame): DataFrame with technical indicators.
            index (int): Row index to analyze (default: last row).
        
        Returns:
            Dict: Summary of technical signals.
        """
        if df.empty or abs(index) > len(df):
            return {}
        
        row = df.iloc[index]
        
        signals = {
            'rsi_signal': 'neutral',
            'macd_signal': 'neutral',
            'stoch_signal': 'neutral',
            'ma_signal': 'neutral',
            'bb_signal': 'neutral',
            'volume_signal': 'neutral'
        }
        
        # RSI signals
        if not pd.isna(row.get('rsi')):
            if row['rsi'] < 30:
                signals['rsi_signal'] = 'oversold'
            elif row['rsi'] > 70:
                signals['rsi_signal'] = 'overbought'
            elif 40 <= row['rsi'] <= 60:
                signals['rsi_signal'] = 'bullish'
        
        # MACD signals
        if not pd.isna(row.get('macd')) and not pd.isna(row.get('macd_signal')):
            if row['macd'] > row['macd_signal']:
                signals['macd_signal'] = 'bullish'
            else:
                signals['macd_signal'] = 'bearish'
        
        # Stochastic signals
        if not pd.isna(row.get('stoch_k')) and not pd.isna(row.get('stoch_d')):
            if row['stoch_k'] < 20 and row['stoch_d'] < 20:
                signals['stoch_signal'] = 'oversold'
            elif row['stoch_k'] > 80 and row['stoch_d'] > 80:
                signals['stoch_signal'] = 'overbought'
            elif row['stoch_k'] > row['stoch_d']:
                signals['stoch_signal'] = 'bullish'
        
        # Moving average signals
        if not pd.isna(row.get('sma_20')) and not pd.isna(row.get('sma_50')):
            if row['close'] > row['sma_20'] > row['sma_50']:
                signals['ma_signal'] = 'bullish'
            elif row['close'] < row['sma_20'] < row['sma_50']:
                signals['ma_signal'] = 'bearish'
        
        # Bollinger Band signals
        if not pd.isna(row.get('bb_upper')) and not pd.isna(row.get('bb_lower')):
            if row['close'] > row['bb_upper']:
                signals['bb_signal'] = 'overbought'
            elif row['close'] < row['bb_lower']:
                signals['bb_signal'] = 'oversold'
        
        # Volume signals
        if not pd.isna(row.get('volume_ratio')):
            if row['volume_ratio'] > 1.5:
                signals['volume_signal'] = 'high'
            elif row['volume_ratio'] < 0.5:
                signals['volume_signal'] = 'low'
        
        return signals
