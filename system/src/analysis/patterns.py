"""
Pattern detection system for swing trading setups.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from src.analysis.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class PatternDetector:
    """
    Detects common swing trading patterns and setups.
    """
    
    def __init__(self):
        """Initialize the pattern detector."""
        self.indicators = TechnicalIndicators()
    
    def detect_breakout_patterns(self, df: pd.DataFrame, lookback: int = 20) -> pd.Series:
        """
        Detect breakout patterns (price breaking above resistance).
        
        Args:
            df (pd.DataFrame): OHLCV data with indicators.
            lookback (int): Number of periods to look back for resistance.
        
        Returns:
            pd.Series: Boolean series indicating breakout signals.
        """
        if len(df) < lookback + 1:
            return pd.Series([False] * len(df), index=df.index)
        
        breakouts = []
        
        for i in range(len(df)):
            if i < lookback:
                breakouts.append(False)
                continue
            
            # Get recent high (resistance level)
            recent_data = df.iloc[i-lookback:i]
            resistance = recent_data['high'].max()
            
            # Check for breakout
            current_close = df.iloc[i]['close']
            current_volume = df.iloc[i]['volume']
            avg_volume = recent_data['volume'].mean()
            
            # Breakout conditions
            is_breakout = (
                current_close > resistance and  # Price breaks resistance
                current_volume > avg_volume * 1.2  # Volume confirmation
            )
            
            breakouts.append(is_breakout)
        
        return pd.Series(breakouts, index=df.index)
    
    def detect_pullback_patterns(self, df: pd.DataFrame, ma_period: int = 20) -> pd.Series:
        """
        Detect pullback to moving average patterns.
        
        Args:
            df (pd.DataFrame): OHLCV data with indicators.
            ma_period (int): Moving average period.
        
        Returns:
            pd.Series: Boolean series indicating pullback signals.
        """
        if f'sma_{ma_period}' not in df.columns:
            df[f'sma_{ma_period}'] = self.indicators.sma(df['close'], ma_period)
        
        pullbacks = []
        
        for i in range(len(df)):
            if i < ma_period + 5:  # Need enough data
                pullbacks.append(False)
                continue
            
            current_row = df.iloc[i]
            ma_value = current_row[f'sma_{ma_period}']
            
            if pd.isna(ma_value):
                pullbacks.append(False)
                continue
            
            # Check if price is near MA (within 2%)
            price_near_ma = abs(current_row['close'] - ma_value) / ma_value < 0.02
            
            # Check if trend was previously up
            prev_5_days = df.iloc[i-5:i]
            was_above_ma = (prev_5_days['close'] > prev_5_days[f'sma_{ma_period}']).mean() > 0.6
            
            # Volume should be lower than average (selling exhaustion)
            if 'volume_sma' in df.columns:
                volume_low = current_row['volume'] < current_row['volume_sma'] * 0.8
            else:
                volume_low = True
            
            is_pullback = price_near_ma and was_above_ma and volume_low
            pullbacks.append(is_pullback)
        
        return pd.Series(pullbacks, index=df.index)
    
    def detect_flag_patterns(self, df: pd.DataFrame, min_flagpole: float = 0.05) -> pd.Series:
        """
        Detect flag patterns (consolidation after strong move).
        
        Args:
            df (pd.DataFrame): OHLCV data.
            min_flagpole (float): Minimum percentage move for flagpole.
        
        Returns:
            pd.Series: Boolean series indicating flag patterns.
        """
        flags = []
        
        for i in range(len(df)):
            if i < 15:  # Need enough data
                flags.append(False)
                continue
            
            # Look for strong move (flagpole) in last 5-10 days
            flagpole_start = max(0, i - 10)
            flagpole_data = df.iloc[flagpole_start:i-5]
            
            if len(flagpole_data) < 3:
                flags.append(False)
                continue
            
            # Check for strong upward move
            price_change = (flagpole_data['close'].iloc[-1] / flagpole_data['close'].iloc[0]) - 1
            
            if price_change < min_flagpole:
                flags.append(False)
                continue
            
            # Check for consolidation (flag) in last 5 days
            consolidation_data = df.iloc[i-5:i+1]
            price_range = (consolidation_data['high'].max() - consolidation_data['low'].min()) / consolidation_data['close'].mean()
            
            # Flag should be tight consolidation
            is_flag = price_range < 0.05  # Less than 5% range
            
            flags.append(is_flag)
        
        return pd.Series(flags, index=df.index)
    
    def detect_cup_and_handle(self, df: pd.DataFrame, min_cup_depth: float = 0.10) -> pd.Series:
        """
        Detect cup and handle patterns.
        
        Args:
            df (pd.DataFrame): OHLCV data.
            min_cup_depth (float): Minimum cup depth as percentage.
        
        Returns:
            pd.Series: Boolean series indicating cup and handle patterns.
        """
        patterns = []
        
        for i in range(len(df)):
            if i < 30:  # Need enough data for cup formation
                patterns.append(False)
                continue
            
            # Look for cup formation in last 20-30 days
            cup_data = df.iloc[i-30:i-5]
            
            if len(cup_data) < 20:
                patterns.append(False)
                continue
            
            # Find cup high and low
            cup_high = cup_data['high'].max()
            cup_low = cup_data['low'].min()
            cup_depth = (cup_high - cup_low) / cup_high
            
            if cup_depth < min_cup_depth:
                patterns.append(False)
                continue
            
            # Check for handle formation (last 5 days)
            handle_data = df.iloc[i-5:i+1]
            handle_high = handle_data['high'].max()
            handle_low = handle_data['low'].min()
            
            # Handle should be smaller than cup and near cup high
            handle_depth = (handle_high - handle_low) / handle_high
            near_cup_high = abs(handle_high - cup_high) / cup_high < 0.05
            
            is_cup_handle = (
                handle_depth < cup_depth * 0.5 and  # Handle smaller than cup
                near_cup_high  # Handle near cup high
            )
            
            patterns.append(is_cup_handle)
        
        return pd.Series(patterns, index=df.index)
    
    def detect_golden_cross(self, df: pd.DataFrame) -> pd.Series:
        """
        Detect golden cross patterns (50-day MA crossing above 200-day MA).
        
        Args:
            df (pd.DataFrame): OHLCV data with moving averages.
        
        Returns:
            pd.Series: Boolean series indicating golden cross signals.
        """
        if 'sma_50' not in df.columns:
            df['sma_50'] = self.indicators.sma(df['close'], 50)
        if 'sma_200' not in df.columns:
            df['sma_200'] = self.indicators.sma(df['close'], 200)
        
        # Golden cross occurs when 50-day MA crosses above 200-day MA
        golden_cross = (
            (df['sma_50'] > df['sma_200']) &  # 50 MA above 200 MA
            (df['sma_50'].shift(1) <= df['sma_200'].shift(1))  # Was below yesterday
        )
        
        return golden_cross
    
    def detect_rsi_divergence(self, df: pd.DataFrame, lookback: int = 14) -> pd.Series:
        """
        Detect RSI bullish divergence patterns.
        
        Args:
            df (pd.DataFrame): OHLCV data with RSI.
            lookback (int): Lookback period for divergence detection.
        
        Returns:
            pd.Series: Boolean series indicating RSI divergence.
        """
        if 'rsi' not in df.columns:
            df['rsi'] = self.indicators.rsi(df['close'])
        
        divergences = []
        
        for i in range(len(df)):
            if i < lookback * 2:
                divergences.append(False)
                continue
            
            # Look for two recent lows
            recent_data = df.iloc[i-lookback:i+1]
            
            # Find price lows
            price_lows = recent_data[recent_data['low'] == recent_data['low'].rolling(5, center=True).min()]
            
            if len(price_lows) < 2:
                divergences.append(False)
                continue
            
            # Get the two most recent lows
            low1_idx = price_lows.index[-2]
            low2_idx = price_lows.index[-1]
            
            low1_price = df.loc[low1_idx, 'low']
            low2_price = df.loc[low2_idx, 'low']
            low1_rsi = df.loc[low1_idx, 'rsi']
            low2_rsi = df.loc[low2_idx, 'rsi']
            
            # Bullish divergence: price makes lower low, RSI makes higher low
            is_divergence = (
                low2_price < low1_price and  # Price lower low
                low2_rsi > low1_rsi and      # RSI higher low
                low2_rsi < 40                # RSI in oversold territory
            )
            
            divergences.append(is_divergence)
        
        return pd.Series(divergences, index=df.index)
    
    def detect_volume_breakout(self, df: pd.DataFrame, volume_multiplier: float = 2.0) -> pd.Series:
        """
        Detect volume breakout patterns.
        
        Args:
            df (pd.DataFrame): OHLCV data.
            volume_multiplier (float): Volume multiplier threshold.
        
        Returns:
            pd.Series: Boolean series indicating volume breakouts.
        """
        if 'volume_sma' not in df.columns:
            df['volume_sma'] = self.indicators.volume_sma(df['volume'])
        
        # Volume breakout when current volume > average volume * multiplier
        volume_breakout = (
            (df['volume'] > df['volume_sma'] * volume_multiplier) &
            (df['close'] > df['open'])  # Bullish price action
        )
        
        return volume_breakout
    
    def get_all_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect all patterns and add them to the DataFrame.
        
        Args:
            df (pd.DataFrame): OHLCV data with indicators.
        
        Returns:
            pd.DataFrame: DataFrame with all pattern signals.
        """
        df = df.copy()
        
        # Detect all patterns
        df['breakout'] = self.detect_breakout_patterns(df)
        df['pullback'] = self.detect_pullback_patterns(df)
        df['flag'] = self.detect_flag_patterns(df)
        df['cup_handle'] = self.detect_cup_and_handle(df)
        df['golden_cross'] = self.detect_golden_cross(df)
        df['rsi_divergence'] = self.detect_rsi_divergence(df)
        df['volume_breakout'] = self.detect_volume_breakout(df)
        
        # Create composite pattern score
        pattern_columns = ['breakout', 'pullback', 'flag', 'cup_handle', 
                          'golden_cross', 'rsi_divergence', 'volume_breakout']
        df['pattern_score'] = df[pattern_columns].sum(axis=1)
        
        return df
    
    def get_pattern_summary(self, df: pd.DataFrame, index: int = -1) -> Dict:
        """
        Get pattern summary for a specific row.
        
        Args:
            df (pd.DataFrame): DataFrame with pattern signals.
            index (int): Row index to analyze.
        
        Returns:
            Dict: Pattern summary.
        """
        if df.empty or abs(index) > len(df):
            return {}
        
        row = df.iloc[index]
        
        patterns = {
            'breakout': bool(row.get('breakout', False)),
            'pullback': bool(row.get('pullback', False)),
            'flag': bool(row.get('flag', False)),
            'cup_handle': bool(row.get('cup_handle', False)),
            'golden_cross': bool(row.get('golden_cross', False)),
            'rsi_divergence': bool(row.get('rsi_divergence', False)),
            'volume_breakout': bool(row.get('volume_breakout', False)),
            'total_patterns': int(row.get('pattern_score', 0))
        }
        
        return patterns
