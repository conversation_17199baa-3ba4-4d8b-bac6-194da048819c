# Daily Swing Trading System

## 🎯 Quick Start - Daily Trading Routine

### Your Daily Files (Run These Every Day)

1. **`daily_trading_workflow.py`** - Complete daily routine (START HERE)
   ```bash
   python daily_trading_workflow.py
   ```

2. **`daily_swing_screener.py`** - Find new trading opportunities
   ```bash
   python daily_swing_screener.py
   ```

3. **`daily_position_tracker.py`** - Monitor existing positions
   ```bash
   python daily_position_tracker.py
   ```

### Adding New Positions
```bash
# Add a new position to track
python daily_position_tracker.py --add --ticker AAPL --price 150.00 --shares 100
```

---

## 📊 Strategy Overview

**Universal Swing Strategy** (Validated Performance)
- **Win Rate:** 60.7%
- **Profit Factor:** 1.38
- **Stop Loss:** 5%
- **Take Profit:** 8%
- **Max Hold Period:** 8 days
- **Position Size:** 2-3% per trade
- **Max Positions:** 5 concurrent

---

## 📁 Directory Structure

```
SwingTrading/
├── daily_trading_workflow.py     # 🚀 MAIN DAILY SCRIPT
├── daily_swing_screener.py       # 🔍 Find opportunities
├── daily_position_tracker.py     # 📊 Monitor positions
├── daily_results/                # 📈 All trading results
│   ├── active_positions.json     # Current positions
│   ├── screening_results_*.json  # Daily screening results
│   └── position_tracking_*.json  # Position monitoring results
├── src/                          # 🔧 Core system (don't modify)
├── system/                       # 📚 Background files
│   ├── logs/                     # System logs
│   ├── research/                 # Research & validation files
│   └── config/                   # Configuration files
└── README_DAILY_TRADING.md       # 📖 This guide
```

---

## 🔄 Daily Workflow

### Morning Routine (Before Market Open)
1. Run `python daily_trading_workflow.py`
2. Review position monitoring results
3. Check for exit signals on existing positions
4. Review new trade opportunities
5. Verify entry prices are still valid
6. Prepare orders with stop loss/take profit levels

### During Market Hours
1. Place new orders if recommended
2. Monitor existing positions
3. Execute any required exits immediately

### After Market Close
1. Update position records if needed
2. Review daily performance
3. Plan for next trading day

---

## 🎯 Trade Management Rules

### Entry Rules
- Only trade during **Bullish** market regime
- Signal strength must be ≥ 60%
- Maximum 5 concurrent positions
- Position size: 2-3% of portfolio per trade

### Exit Rules
- **Stop Loss:** 5% below entry price
- **Take Profit:** 8% above entry price
- **Time Stop:** 8 days maximum hold
- **Regime Change:** Exit if market turns bearish

### Risk Management
- Total portfolio risk: <15%
- Never risk more than 3% per trade
- Always use stop losses
- Monitor positions daily

---

## 📈 Performance Tracking

All results are automatically saved to `daily_results/`:
- **Screening Results:** New opportunities found each day
- **Position Tracking:** Performance of active positions
- **Trade History:** Complete record of all trades

---

## 🚨 Important Notes

1. **Market Regime:** Only trade during bullish market conditions
2. **Signal Quality:** Focus on signals with 60%+ strength
3. **Risk First:** Always set stop losses before entering trades
4. **Discipline:** Follow the system rules consistently
5. **Review:** Check results daily and adjust if needed

---

## 🛠️ Troubleshooting

### Common Issues
- **No signals found:** Normal during bearish/neutral markets
- **API errors:** Check internet connection and API key
- **Missing data:** Some stocks may have data issues

### Getting Help
- Check `system/logs/` for error details
- Ensure all dependencies are installed
- Verify API key is working

---

## 📚 Strategy Background

This system is based on comprehensive research of 500 S&P stocks over 5 years (2020-2025), testing multiple strategies across different market regimes. The Universal Swing Strategy was selected for its:

- Consistent performance across market conditions
- Realistic win rates (not inflated backtesting)
- Proper risk management
- Statistical significance (200+ trades tested)

The system automatically adapts to market regimes and only recommends trades when conditions are favorable.

---

**🎯 Remember:** Consistency and discipline are key to successful swing trading. Follow the system, manage risk, and stay patient for quality setups.
