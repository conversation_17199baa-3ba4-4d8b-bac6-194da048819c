# 🎯 RSI Oversold Bounce Trading System

**The ONLY statistically proven profitable swing trading strategy.**

## 🏆 Quick Stats
- **Win Rate**: 58.7% (proven over 5,256 trades)
- **Profit Factor**: 2.52
- **Average Return**: 2.03% per trade
- **Hold Period**: Exactly 2 days
- **Portfolio**: $813.65 USD

---

## 🚀 Daily Trading Workflow

### **Morning Routine (9:00 AM)**
```bash
cd DAILY_TRADING
python3 enhanced_daily_screener.py
```
**Result**: Find RSI < 25 signals in 100 stocks (45 seconds)

### **Execute Best Trade**
- Buy the strongest RSI signal at market open
- Set 2% stop loss immediately
- Set calendar reminder to sell in exactly 2 days

### **Monitor Positions (Optional)**
```bash
python3 real_time_position_monitor.py    # Live P&L tracking
python3 live_rsi_alerts.py              # Background signal detection
```

### **Track Performance**
```bash
python3 position_tracker.py             # Record trades and results
```

---

## 📁 Directory Structure

```
SwingTrading/
├── 📊 DAILY_TRADING/           # Your daily tools (START HERE)
│   ├── enhanced_daily_screener.py     # Find signals (MAIN TOOL)
│   ├── position_tracker.py            # Track trades
│   ├── real_time_position_monitor.py  # Live monitoring
│   └── live_rsi_alerts.py            # Background alerts
│
├── 📁 results/                 # All your trading results
│   └── daily_results/         # Screening results and trade journals
│
├── 📚 docs/                    # Documentation and guides
├── 🗄️ archive/                # Research and historical data
└── 🔧 system/                  # Technical files (ignore for daily use)
```

---

## 🎯 Strategy Rules (SIMPLE)

### **Entry**
- RSI < 25 (oversold)
- Any liquid stock

### **Exit**
- Hold exactly 2 days
- 2% stop loss

### **That's it!** 
No other indicators, no complex analysis. Pure RSI oversold bounce.

---

## 📊 Recent Performance

### **Latest Signals** (Friday, June 13, 2025)
1. **OKTA**: RSI 15.1 (STRONG) - Ready for Monday
2. **KMB**: RSI 18.5 (STRONG)
3. **K**: RSI 19.9 (STRONG)

### **Recent Trade**
- **MCD**: Entered Tuesday $301.19, RSI 12.3 (VERY STRONG)
- **Lesson**: Follow 2-day exit rule exactly

---

## ⚡ Quick Start

1. **Find Today's Signals**:
   ```bash
   cd DAILY_TRADING
   python3 enhanced_daily_screener.py
   ```

2. **Execute Best Trade**:
   - Buy strongest RSI signal
   - Set stop loss at 2% below entry
   - Exit in exactly 2 days

3. **Track Results**:
   ```bash
   python3 position_tracker.py
   ```

---

## 🎯 Next Trade: OKTA

**Signal**: RSI 15.1 (STRONG)  
**Plan**: Buy Monday, sell Wednesday  
**Expected**: 2.03% return (~$16.56 profit)  
**Risk**: 2% stop loss (~$16.32 max loss)

---

## 📞 Need Help?

- **Documentation**: Check `docs/` folder
- **Results**: Check `results/daily_results/`
- **System Issues**: Check `system/` folder

**Remember**: Keep it simple. RSI < 25 → Buy → Hold 2 days → Sell. That's it! 🚀
