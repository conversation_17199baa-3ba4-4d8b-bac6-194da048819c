{"timestamp": "20250609_173202", "analysis_summary": {"total_strategies_tested": 5, "data_period": "5 years (2020-2025)", "stocks_analyzed": 150, "best_strategy_found": true}, "all_strategies": {"rsi_oversold_bounce": {"strategy_name": "RSI Oversold Bounce (RSI<20, 5d hold)", "total_trades": 5256, "win_rate": 58.65677321156774, "avg_return": 2.0279561689196037, "total_return": 10658.937623841437, "profit_factor": 2.520280859644348, "avg_win": 5.731463214914216, "avg_loss": -3.2414070585941253, "max_drawdown": -87.71652438143899, "p_value": 4.284342635276864e-05, "statistically_significant": "True", "score": 1.4783154281361348}, "simple_rsi": {"strategy_name": "Simple RSI Mean Reversion", "total_trades": 20049, "win_rate": 54.85061599082248, "avg_return": 0.7115696797944914, "total_return": 14266.260510199758, "profit_factor": 1.5912421942167958, "avg_win": 3.491457752547687, "avg_loss": -2.681927352958448, "max_drawdown": -98.4680654322799, "p_value": 1.4592190973489074e-08, "statistically_significant": "True", "score": 0.8728061454337923}, "buy_and_hold": {"strategy_name": "Buy and Hold (20-day chunks)", "total_trades": 9227, "win_rate": 53.245908746071315, "avg_return": 1.093877048199526, "total_return": 10093.203523737026, "profit_factor": 1.387341274005477, "avg_win": 7.358203536722509, "avg_loss": -6.048665378872021, "max_drawdown": -99.77465188080077, "p_value": 1.0529428326100723e-12, "statistically_significant": "True", "score": 0.7387024687535394}, "bb_bounce": {"strategy_name": "Bollinger Band Bounce", "total_trades": 9110, "win_rate": 53.38090010976948, "avg_return": 0.4002384549552963, "total_return": 3646.172324642749, "profit_factor": 1.314269275425212, "avg_win": 3.1355617052439637, "avg_loss": -2.742804786751453, "max_drawdown": -99.01538807379701, "p_value": 1.5487434665027832e-19, "statistically_significant": "True", "score": 0.7015687690881236}, "price_momentum": {"strategy_name": "Price Momentum (5d >3%)", "total_trades": 40759, "win_rate": 50.86729311317746, "avg_return": 0.05838590516239001, "total_return": 2379.7511085138544, "profit_factor": 1.0517614276610643, "avg_win": 2.332278362612467, "avg_loss": -2.307537451492191, "max_drawdown": -99.99901174777334, "p_value": 0.0007306646053561626, "statistically_significant": "True", "score": 0.5350025682596935}}, "best_strategy": ["rsi_oversold_bounce", {"strategy_name": "RSI Oversold Bounce (RSI<20, 5d hold)", "total_trades": 5256, "win_rate": 58.65677321156774, "avg_return": 2.0279561689196037, "total_return": 10658.937623841437, "profit_factor": 2.520280859644348, "avg_win": 5.731463214914216, "avg_loss": -3.2414070585941253, "max_drawdown": -87.71652438143899, "p_value": 4.284342635276864e-05, "statistically_significant": "True", "score": 1.4783154281361348}], "recommendations": {"recommended_strategy": "rsi_oversold_bounce", "expected_performance": {"win_rate": "58.7%", "profit_factor": "2.52", "avg_return_per_trade": "2.03%"}, "implementation_notes": ["Buy when RSI drops below threshold", "Use fixed holding periods", "Best in trending markets"], "risk_warnings": ["Past performance does not guarantee future results", "Use proper position sizing (1-2% risk per trade)", "Always use stop losses", "Start with paper trading", "Market conditions can change"]}}