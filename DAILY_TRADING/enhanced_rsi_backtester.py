#!/usr/bin/env python3
"""
Enhanced RSI Strategy Backtesting
=================================

Test RSI < 25 strategy with volume and resistance zone enhancements
to improve the baseline 58.7% win rate.

Enhancements to Test:
1. RSI + Volume Spike (>1.5x average)
2. RSI + Resistance Zone Touch (near recent highs)
3. RSI + Volume + Resistance Combined

Research Parameters:
- 200 S&P 500 stocks
- 5 years of data (2020-2025)
- Same 2-day hold, 2% stop loss rules
- Statistical significance testing

Usage:
    python3 enhanced_rsi_backtester.py
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system', 'src'))

from data.data_manager import DataManager
from analysis.indicators import TechnicalIndicators
from api.polygon_client import PolygonClient

class EnhancedRSIBacktester:
    """Enhanced RSI strategy backtesting with volume and resistance filters."""
    
    def __init__(self):
        """Initialize the enhanced RSI backtester."""
        self.data_manager = DataManager(cache_enabled=True)  # Use cache for faster backtesting
        self.indicators = TechnicalIndicators()
        
        # Strategy parameters
        self.rsi_threshold = 25
        self.hold_days = 2
        self.stop_loss_pct = 2
        
        # Enhancement parameters
        self.volume_spike_threshold = 1.5  # Volume > 1.5x average
        self.resistance_lookback = 20      # Look back 20 days for resistance
        self.resistance_tolerance = 0.02   # Within 2% of resistance level
        
        # Test period
        self.start_date = "2020-01-01"
        self.end_date = "2025-06-17"
        
        # Expanded stock universe (200 stocks)
        self.stock_universe = self._get_200_stock_universe()
        
    def _get_200_stock_universe(self) -> List[str]:
        """Get 200 liquid S&P 500 stocks for testing."""
        return [
            # Large Cap Tech
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'ADBE', 'CRM',
            'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN', 'CSCO', 'NOW', 'INTU', 'PANW',
            
            # Financial Services
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'USB', 'PNC', 'TFC',
            'COF', 'SCHW', 'BLK', 'SPGI', 'ICE', 'CME', 'AON', 'MMC', 'CB', 'PGR',
            
            # Healthcare & Pharma
            'UNH', 'JNJ', 'PFE', 'ABBV', 'MRK', 'LLY', 'TMO', 'ABT', 'DHR', 'BMY',
            'AMGN', 'GILD', 'REGN', 'BIIB', 'CI', 'HUM', 'CVS', 'BSX', 'SYK', 'MDT',
            
            # Consumer Goods
            'WMT', 'HD', 'COST', 'LOW', 'TGT', 'TJX', 'NKE', 'SBUX', 'MCD', 'YUM',
            'PG', 'KO', 'PEP', 'CL', 'KMB', 'CLX', 'HSY', 'K', 'GIS', 'CAG',
            
            # Industrial & Materials
            'CAT', 'BA', 'HON', 'UNP', 'LMT', 'RTX', 'GE', 'MMM', 'EMR', 'ETN',
            'ITW', 'PH', 'CMI', 'DE', 'NOC', 'GD', 'LIN', 'APD', 'ECL', 'SHW',
            
            # Energy & Utilities
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO', 'PSX', 'OKE', 'KMI',
            'NEE', 'DUK', 'SO', 'EXC', 'D', 'AEP', 'XEL', 'SRE', 'ES', 'AWK',
            
            # Communication & Media
            'CMCSA', 'DIS', 'NFLX', 'T', 'VZ', 'CHTR', 'TMUS', 'DISH', 'MTCH', 'PINS',
            
            # Real Estate & REITs
            'PLD', 'AMT', 'CCI', 'EQIX', 'PSA', 'EXR', 'AVB', 'EQR', 'UDR', 'ESS',
            
            # Retail & E-commerce
            'AMZN', 'WMT', 'HD', 'COST', 'LOW', 'TGT', 'TJX', 'ULTA', 'ORLY', 'AZO',
            
            # Biotech & Growth
            'MRNA', 'REGN', 'BIIB', 'GILD', 'VRTX', 'ILMN', 'DXCM', 'ISRG', 'ALGN', 'IDXX',
            
            # Fintech & New Economy
            'V', 'MA', 'PYPL', 'SQ', 'COIN', 'HOOD', 'SOFI', 'CRWD', 'OKTA', 'DDOG',
            
            # Travel & Leisure
            'CCL', 'RCL', 'MAR', 'HLT', 'LVS', 'WYNN', 'MGM', 'DAL', 'UAL', 'AAL',
            
            # Food & Beverage
            'KO', 'PEP', 'MCD', 'SBUX', 'YUM', 'QSR', 'DPZ', 'TAP', 'KHC', 'CPB',
            'HSY', 'K', 'GIS', 'CAG', 'SJM', 'HRL', 'TSN', 'MDLZ', 'CLX', 'CHD'
        ]
    
    def calculate_resistance_level(self, data: pd.DataFrame, index: int) -> float:
        """
        Calculate resistance level based on recent highs.
        
        Args:
            data: Price data DataFrame
            index: Current index position
            
        Returns:
            Resistance level price
        """
        if index < self.resistance_lookback:
            return np.nan
            
        # Look back for recent highs
        lookback_data = data.iloc[max(0, index - self.resistance_lookback):index]
        
        if len(lookback_data) < 5:
            return np.nan
            
        # Find the highest high in the lookback period
        resistance = lookback_data['high'].max()
        
        return resistance
    
    def is_near_resistance(self, current_price: float, resistance: float) -> bool:
        """
        Check if current price is near resistance level.
        
        Args:
            current_price: Current stock price
            resistance: Resistance level
            
        Returns:
            True if near resistance, False otherwise
        """
        if pd.isna(resistance) or resistance <= 0:
            return False
            
        # Check if price is within tolerance of resistance
        distance_pct = abs(current_price - resistance) / resistance
        return distance_pct <= self.resistance_tolerance
    
    def test_baseline_rsi(self, data: pd.DataFrame) -> List[Dict]:
        """Test baseline RSI < 25 strategy."""
        signals = []
        
        for i in range(len(data)):
            row = data.iloc[i]
            
            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue
                
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'signal_type': 'BASELINE_RSI'
            })
            
        return signals
    
    def test_rsi_plus_volume(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Volume Spike strategy."""
        signals = []
        
        for i in range(len(data)):
            row = data.iloc[i]
            
            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue
                
            # Volume spike condition
            if pd.isna(row.get('volume_ratio')) or row['volume_ratio'] < self.volume_spike_threshold:
                continue
                
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'volume_ratio': row['volume_ratio'],
                'signal_type': 'RSI_VOLUME_SPIKE'
            })
            
        return signals
    
    def test_rsi_plus_resistance(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Near Resistance strategy."""
        signals = []
        
        for i in range(len(data)):
            row = data.iloc[i]
            
            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue
                
            # Calculate resistance level
            resistance = self.calculate_resistance_level(data, i)
            
            # Check if near resistance
            if not self.is_near_resistance(row['close'], resistance):
                continue
                
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'resistance_level': resistance,
                'distance_to_resistance': abs(row['close'] - resistance) / resistance * 100,
                'signal_type': 'RSI_RESISTANCE'
            })
            
        return signals
    
    def test_rsi_volume_resistance(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Volume Spike + Near Resistance strategy."""
        signals = []
        
        for i in range(len(data)):
            row = data.iloc[i]
            
            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue
                
            # Volume spike condition
            if pd.isna(row.get('volume_ratio')) or row['volume_ratio'] < self.volume_spike_threshold:
                continue
                
            # Calculate resistance level
            resistance = self.calculate_resistance_level(data, i)
            
            # Check if near resistance
            if not self.is_near_resistance(row['close'], resistance):
                continue
                
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'volume_ratio': row['volume_ratio'],
                'resistance_level': resistance,
                'distance_to_resistance': abs(row['close'] - resistance) / resistance * 100,
                'signal_type': 'RSI_VOLUME_RESISTANCE'
            })
            
        return signals

    def simulate_trades(self, signals: List[Dict], data: pd.DataFrame) -> Dict:
        """
        Simulate trades for a list of signals.

        Args:
            signals: List of trading signals
            data: Price data DataFrame

        Returns:
            Dictionary with trade results
        """
        if not signals:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'trades': []
            }

        trades = []
        data_dict = data.set_index('date').to_dict('index')

        for signal in signals:
            entry_date = pd.to_datetime(signal['date']).date()
            entry_price = signal['entry_price']

            # Calculate exit date (2 trading days later)
            exit_date = self._add_trading_days(entry_date, self.hold_days)

            # Find exit price
            exit_price = None
            stop_loss_price = entry_price * (1 - self.stop_loss_pct / 100)

            # Check each day during hold period for stop loss
            current_date = entry_date
            hit_stop_loss = False

            for day in range(self.hold_days + 1):
                check_date = self._add_trading_days(entry_date, day)

                if check_date in data_dict:
                    day_data = data_dict[check_date]

                    # Check if stop loss hit during the day
                    if day_data['low'] <= stop_loss_price:
                        exit_price = stop_loss_price
                        exit_date = check_date
                        hit_stop_loss = True
                        break

            # If no stop loss, exit at planned date
            if not hit_stop_loss and exit_date in data_dict:
                exit_price = data_dict[exit_date]['close']

            # Calculate trade result
            if exit_price is not None:
                return_pct = (exit_price / entry_price - 1) * 100
                is_winner = return_pct > 0

                trade = {
                    'entry_date': entry_date,
                    'exit_date': exit_date,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'return_pct': return_pct,
                    'is_winner': is_winner,
                    'hit_stop_loss': hit_stop_loss,
                    'signal_data': signal
                }
                trades.append(trade)

        # Calculate performance metrics
        if not trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'trades': []
            }

        returns = [trade['return_pct'] for trade in trades]
        winners = [r for r in returns if r > 0]
        losers = [r for r in returns if r <= 0]

        win_rate = len(winners) / len(trades) * 100
        avg_return = np.mean(returns)
        total_return = sum(returns)

        # Profit factor
        total_gains = sum(winners) if winners else 0
        total_losses = abs(sum(losers)) if losers else 1
        profit_factor = total_gains / total_losses if total_losses > 0 else 0

        # Max drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = running_max - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

        return {
            'total_trades': len(trades),
            'win_rate': win_rate,
            'avg_return': avg_return,
            'profit_factor': profit_factor,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'trades': trades
        }

    def _add_trading_days(self, start_date, days: int):
        """Add trading days to a date (skip weekends)."""
        current_date = start_date
        days_added = 0

        while days_added < days:
            current_date += timedelta(days=1)
            # Skip weekends
            if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                days_added += 1

        return current_date

    def _combine_results(self, ticker_results: List[Dict]) -> Dict:
        """Combine results from multiple tickers."""
        if not ticker_results:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0
            }

        all_trades = []
        for result in ticker_results:
            all_trades.extend(result.get('trades', []))

        if not all_trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0
            }

        returns = [trade['return_pct'] for trade in all_trades]
        winners = [r for r in returns if r > 0]
        losers = [r for r in returns if r <= 0]

        win_rate = len(winners) / len(all_trades) * 100
        avg_return = np.mean(returns)
        total_return = sum(returns)

        # Profit factor
        total_gains = sum(winners) if winners else 0
        total_losses = abs(sum(losers)) if losers else 1
        profit_factor = total_gains / total_losses if total_losses > 0 else 0

        # Max drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = running_max - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

        return {
            'total_trades': len(all_trades),
            'win_rate': win_rate,
            'avg_return': avg_return,
            'profit_factor': profit_factor,
            'total_return': total_return,
            'max_drawdown': max_drawdown
        }

    def _display_strategy_results(self, strategy_name: str, results: Dict):
        """Display strategy results."""
        print(f"\n📊 {strategy_name} RESULTS:")
        print("-" * 50)
        print(f"   Total Trades: {results['total_trades']}")
        print(f"   Win Rate: {results['win_rate']:.1f}%")
        print(f"   Avg Return: {results['avg_return']:.2f}%")
        print(f"   Profit Factor: {results['profit_factor']:.2f}")
        print(f"   Total Return: {results['total_return']:.1f}%")
        print(f"   Max Drawdown: {results['max_drawdown']:.1f}%")

        # Compare to baseline
        baseline_win_rate = 58.7
        if results['win_rate'] > baseline_win_rate:
            improvement = results['win_rate'] - baseline_win_rate
            print(f"   🎯 IMPROVEMENT: +{improvement:.1f}% vs baseline!")
        elif results['win_rate'] < baseline_win_rate:
            decline = baseline_win_rate - results['win_rate']
            print(f"   ⚠️  DECLINE: -{decline:.1f}% vs baseline")
        else:
            print(f"   ➡️  Same as baseline")

    def run_comprehensive_backtest(self) -> Dict:
        """
        Run comprehensive backtest of all RSI enhancement strategies.

        Returns:
            Dictionary with all strategy results
        """
        print("🔬 ENHANCED RSI STRATEGY BACKTESTING")
        print("=" * 80)
        print(f"📊 Testing Period: {self.start_date} to {self.end_date}")
        print(f"📈 Stock Universe: {len(self.stock_universe)} stocks")
        print(f"🎯 Base Strategy: RSI < {self.rsi_threshold}, {self.hold_days}-day hold, {self.stop_loss_pct}% stop")
        print("🔍 Enhancements: Volume Spike (>1.5x), Resistance Zone (±2%)")
        print("=" * 80)

        strategies = {
            'BASELINE_RSI': self.test_baseline_rsi,
            'RSI_VOLUME_SPIKE': self.test_rsi_plus_volume,
            'RSI_RESISTANCE': self.test_rsi_plus_resistance,
            'RSI_VOLUME_RESISTANCE': self.test_rsi_volume_resistance
        }

        all_results = {}

        for strategy_name, strategy_func in strategies.items():
            print(f"\n🧪 Testing {strategy_name}...")

            all_signals = []
            processed_stocks = 0

            for ticker in self.stock_universe:
                try:
                    # Get historical data
                    data = self.data_manager.get_historical_data(
                        ticker, self.start_date, self.end_date
                    )

                    if data.empty or len(data) < 50:
                        continue

                    # Add all indicators
                    data = self.indicators.add_all_indicators(data)

                    # Test strategy
                    signals = strategy_func(data)

                    # Add ticker to signals
                    for signal in signals:
                        signal['ticker'] = ticker

                    all_signals.extend(signals)
                    processed_stocks += 1

                    if processed_stocks % 25 == 0:
                        print(f"   Processed {processed_stocks}/{len(self.stock_universe)} stocks...")

                except Exception as e:
                    print(f"   ⚠️  Error processing {ticker}: {e}")
                    continue

            print(f"   ✅ Found {len(all_signals)} signals from {processed_stocks} stocks")

            # Simulate trades for all signals
            if all_signals:
                # Group signals by ticker and simulate
                ticker_results = []

                for ticker in set(signal['ticker'] for signal in all_signals):
                    ticker_signals = [s for s in all_signals if s['ticker'] == ticker]

                    try:
                        data = self.data_manager.get_historical_data(
                            ticker, self.start_date, self.end_date
                        )

                        if not data.empty:
                            # Add indicators
                            data = self.indicators.add_all_indicators(data)
                            ticker_result = self.simulate_trades(ticker_signals, data)
                            ticker_results.append(ticker_result)

                    except Exception as e:
                        continue

                # Combine all ticker results
                combined_result = self._combine_results(ticker_results)
                all_results[strategy_name] = combined_result

                # Display results
                self._display_strategy_results(strategy_name, combined_result)
            else:
                all_results[strategy_name] = {
                    'total_trades': 0,
                    'win_rate': 0,
                    'avg_return': 0,
                    'profit_factor': 0,
                    'total_return': 0,
                    'max_drawdown': 0
                }

        # Final comparison
        print(f"\n🏆 FINAL COMPARISON:")
        print("=" * 80)
        for strategy, results in all_results.items():
            status = ""
            if results['win_rate'] > 58.7:
                status = "🎯 IMPROVED"
            elif results['win_rate'] < 58.7:
                status = "⚠️  DECLINED"
            else:
                status = "➡️  SAME"

            print(f"{strategy:20} | {results['win_rate']:5.1f}% | {results['total_trades']:4d} trades | {status}")

        # Save results
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"results/enhanced_rsi_backtest_{timestamp}.json"

        os.makedirs('results', exist_ok=True)
        with open(results_file, 'w') as f:
            # Convert numpy types for JSON serialization
            json_results = {}
            for strategy, result in all_results.items():
                json_results[strategy] = {
                    k: float(v) if isinstance(v, (np.integer, np.floating)) else v
                    for k, v in result.items() if k != 'trades'  # Exclude detailed trades
                }
            json.dump(json_results, f, indent=2, default=str)

        print(f"\n💾 Results saved to: {results_file}")

        return all_results


def main():
    """Run enhanced RSI backtesting."""
    backtester = EnhancedRSIBacktester()
    results = backtester.run_comprehensive_backtest()

    print("\n✅ Enhanced RSI backtesting completed!")
    print("🎯 Check results to see if volume or resistance filters improve the 58.7% baseline")


if __name__ == "__main__":
    main()
