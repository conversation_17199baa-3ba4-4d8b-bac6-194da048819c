#!/usr/bin/env python3
"""
Quick Test for Trend Continuation Strategy
==========================================

Test the trend continuation strategy on a small set of stocks to validate
the implementation before running the full 200+ stock backtest.

Usage:
    python3 test_trend_continuation.py
"""

import sys
import os

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
from src.data.data_manager import DataManager
from src.analysis.indicators import TechnicalIndicators
from trend_continuation_strategy import TrendContinuationStrategy

def test_strategy():
    """Test the refined trend continuation strategy on a few stocks."""
    print("🧪 Testing RELAXED Trend Continuation Strategy (Option A)")
    print("=" * 80)
    print("📋 RELAXED EXIT CRITERIA:")
    print("   SPY Regime: Only trade when SPY in confirmed uptrend")
    print("   Entry: EMA 20 > EMA 50 + EMA 20 rising + RSI 30-65 + volume 1.05x")
    print("   Exit: +6% gain OR -5% stop OR below EMA 20 OR 20 days max (RELAXED)")
    print("=" * 80)
    
    # Initialize components with caching enabled
    data_manager = DataManager(cache_enabled=True)  # Enable caching for testing
    indicators = TechnicalIndicators()
    strategy = TrendContinuationStrategy()
    
    # Test stocks
    test_stocks = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA']
    
    # Test period (last 2 years for speed)
    start_date = "2023-01-01"
    end_date = "2025-06-09"
    
    print(f"📊 Testing {len(test_stocks)} stocks from {start_date} to {end_date}")
    print()
    
    total_signals = 0
    
    for ticker in test_stocks:
        try:
            print(f"📈 Testing {ticker}...")
            
            # Get data (with caching enabled)
            data = data_manager.get_historical_data(ticker, start_date, end_date, use_cache=True)
            
            if data.empty:
                print(f"   ❌ No data for {ticker}")
                continue
                
            # Add indicators
            data = indicators.add_all_indicators(data)
            
            # Check for required indicators
            required_indicators = ['ema_20', 'ema_50', 'rsi', 'volume_sma']
            missing = [ind for ind in required_indicators if ind not in data.columns]
            
            if missing:
                print(f"   ⚠️  Missing indicators: {missing}")
                continue
            
            # Generate signals
            signals = strategy.generate_signals(data)
            
            print(f"   ✅ Found {len(signals)} signals")
            
            # Show signal details
            if signals:
                for i, signal in enumerate(signals[:3]):  # Show first 3 signals
                    vol_ratio = signal.get('volume_ratio', 1.0)
                    profit_target = signal.get('profit_target', 6.0)
                    stop_loss = signal.get('stop_loss_pct', 5.0)
                    print(f"      Signal {i+1}: {signal['date']} - RSI: {signal['rsi']:.1f}, Vol: {vol_ratio:.1f}x, Target: +{profit_target}%/-{stop_loss}%, Max: {signal.get('max_hold_days', 20)} days")
            
            total_signals += len(signals)
            
        except Exception as e:
            print(f"   ❌ Error testing {ticker}: {e}")
            continue
    
    print()
    print(f"🎯 Test Results:")
    print(f"   Total Signals: {total_signals}")
    print(f"   Avg per Stock: {total_signals / len(test_stocks):.1f}")
    
    if total_signals > 0:
        print("✅ Strategy implementation appears to be working!")
        print("🚀 Ready to run full backtest with trend_continuation_backtester.py")
    else:
        print("⚠️  No signals found. Check strategy parameters or data.")
    
    print("=" * 50)

if __name__ == "__main__":
    test_strategy()
