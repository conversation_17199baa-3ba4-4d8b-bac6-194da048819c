#!/usr/bin/env python3
"""
Quick Test of RSI + Second Indicator Framework
==============================================

Test the backtesting framework with a small sample of stocks
to validate it works before running the full 150-stock analysis.

Usage:
    python3 test_rsi_plus_indicators.py
"""

import sys
import os
from datetime import datetime

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system', 'src'))

from rsi_plus_indicator_backtester import RSIPlusIndicatorBacktester

class QuickTester(RSIPlusIndicatorBacktester):
    """Quick tester with smaller stock universe."""
    
    def __init__(self):
        super().__init__()
        
        # Use smaller stock universe for quick test
        self.stock_universe = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 
            'TSLA', 'META', 'JPM', 'UNH', 'HD'
        ]
        
        # Shorter test period for speed
        self.start_date = "2024-01-01"
        self.end_date = "2025-06-09"
        
    def run_quick_test(self):
        """Run quick test with limited stocks."""
        print("🧪 QUICK TEST: RSI + Second Indicator Framework")
        print("=" * 60)
        print(f"📊 Testing Period: {self.start_date} to {self.end_date}")
        print(f"📈 Stock Universe: {len(self.stock_universe)} stocks (quick test)")
        print(f"🎯 Base Strategy: RSI < {self.rsi_threshold}, {self.hold_days}-day hold, {self.stop_loss_pct}% stop")
        print("=" * 60)
        
        # Test just a few strategies for validation
        test_strategies = {
            'RSI_VOLUME_SPIKE': self.test_rsi_plus_volume_spike,
            'RSI_MACD_BULLISH': self.test_rsi_plus_macd_bullish,
            'RSI_STOCH_OVERSOLD': self.test_rsi_plus_stoch_oversold
        }
        
        results = {}
        
        for strategy_name, strategy_func in test_strategies.items():
            print(f"\n🧪 Testing {strategy_name}...")
            
            all_signals = []
            processed_stocks = 0
            
            for ticker in self.stock_universe:
                try:
                    print(f"   Processing {ticker}...")
                    
                    # Get stock data
                    data = self.data_manager.get_historical_data(ticker, self.start_date, self.end_date)
                    
                    if data.empty or len(data) < 50:
                        print(f"   ⚠️  Insufficient data for {ticker}")
                        continue
                    
                    # Add all indicators
                    data = self.indicators.add_all_indicators(data)
                    
                    # Test strategy
                    signals = strategy_func(data)
                    
                    # Add ticker to signals
                    for signal in signals:
                        signal['ticker'] = ticker
                    
                    all_signals.extend(signals)
                    processed_stocks += 1
                    
                    print(f"   ✅ {ticker}: {len(signals)} signals found")
                    
                except Exception as e:
                    print(f"   ❌ Error processing {ticker}: {e}")
                    continue
            
            print(f"   📊 Total: {len(all_signals)} signals from {processed_stocks} stocks")
            
            # Quick backtest
            if all_signals:
                # Test with first ticker that has signals
                test_ticker = all_signals[0]['ticker']
                ticker_signals = [s for s in all_signals if s['ticker'] == test_ticker]
                
                try:
                    data = self.data_manager.get_historical_data(test_ticker, self.start_date, self.end_date)
                    data = self.indicators.add_all_indicators(data)
                    
                    result = self.backtest_strategy(ticker_signals, data, strategy_name)
                    results[strategy_name] = result
                    
                    print(f"   🎯 Quick backtest on {test_ticker}:")
                    print(f"      Trades: {result['total_trades']}")
                    print(f"      Win Rate: {result['win_rate']:.1f}%")
                    print(f"      Avg Return: {result['avg_return']:.2f}%")
                    
                except Exception as e:
                    print(f"   ❌ Backtest error: {e}")
                    results[strategy_name] = {
                        'strategy_name': strategy_name,
                        'total_trades': 0,
                        'win_rate': 0,
                        'avg_return': 0,
                        'profit_factor': 0,
                        'total_return': 0,
                        'max_drawdown': 0
                    }
            else:
                results[strategy_name] = {
                    'strategy_name': strategy_name,
                    'total_trades': 0,
                    'win_rate': 0,
                    'avg_return': 0,
                    'profit_factor': 0,
                    'total_return': 0,
                    'max_drawdown': 0
                }
        
        return results


def main():
    """Run quick test."""
    print("🚀 QUICK TEST: RSI + Second Indicator Framework")
    print("This will validate the framework works before running full analysis")
    print("Expected time: 2-5 minutes")
    
    tester = QuickTester()
    
    try:
        # Run quick test
        results = tester.run_quick_test()
        
        # Display results
        print("\n" + "=" * 60)
        print("📊 QUICK TEST RESULTS")
        print("=" * 60)
        
        for strategy_name, result in results.items():
            print(f"\n✅ {strategy_name}:")
            print(f"   Total Trades: {result['total_trades']}")
            print(f"   Win Rate: {result['win_rate']:.1f}%")
            print(f"   Avg Return: {result['avg_return']:.2f}%")
            print(f"   Profit Factor: {result['profit_factor']:.2f}")
        
        print("\n🎯 FRAMEWORK VALIDATION:")
        if any(r['total_trades'] > 0 for r in results.values()):
            print("✅ Framework is working correctly!")
            print("✅ Ready to run full 150-stock analysis")
            print("\n🚀 To run full analysis:")
            print("   python3 rsi_plus_indicator_backtester.py")
        else:
            print("⚠️  No signals found in quick test")
            print("   This might be normal for recent data")
            print("   Full analysis with 5 years of data should find signals")
        
    except Exception as e:
        print(f"\n❌ Quick test failed: {e}")
        print("Please check the error and fix before running full analysis")


if __name__ == "__main__":
    main()
