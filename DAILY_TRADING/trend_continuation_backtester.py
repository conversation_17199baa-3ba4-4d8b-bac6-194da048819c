#!/usr/bin/env python3
"""
Trend Continuation with Pullback Backtesting Framework
=====================================================

Comprehensive backtesting of the Trend Continuation strategy across 200+ stocks
over 5 years to validate the 60-70% win rate target.

Strategy Overview:
- Entry: Pullbacks in strong trends (EMA 20 > EMA 50)
- Pullback Zone: RSI 40-60 (not oversold, but pulled back)
- Patterns: Bull flag, pennant, falling wedge
- Exit: 2-day hold + 2% stop loss
- Target: 60-70% win rate with statistical significance

Research Parameters:
- Stock Universe: 200+ liquid stocks across all sectors
- Time Period: 5 years (2020-2025)
- Minimum Trades: 100+ for statistical significance
- Validation: Out-of-sample testing, regime analysis

Usage:
    python3 trend_continuation_backtester.py
"""

import pandas as pd
import json
import os
from datetime import datetime
from typing import List, Dict
import sys

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
from src.data.data_manager import DataManager
from src.analysis.indicators import TechnicalIndicators
from trend_continuation_strategy import TrendContinuationStrategy

class TrendContinuationBacktester:
    """
    Comprehensive backtesting framework for Trend Continuation strategy.
    """
    
    def __init__(self):
        """Initialize the backtester."""
        self.data_manager = DataManager(cache_enabled=True)  # Enable caching for testing
        self.indicators = TechnicalIndicators()
        self.strategy = TrendContinuationStrategy()
        
        # Research parameters
        self.start_date = "2020-06-10"
        self.end_date = "2025-06-09"
        
        # Expanded stock universe (200+ stocks)
        self.stock_universe = self._get_expanded_stock_universe()
        
        # Results storage
        self.results = {}
        
    def _get_expanded_stock_universe(self) -> List[str]:
        """Get expanded 200+ stock universe for comprehensive testing."""
        # Expanded universe across all sectors for better statistical significance
        return [
            # Technology (40 stocks)
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'ADBE', 'CRM',
            'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN', 'INTU', 'AMAT', 'MU', 'LRCX',
            'CSCO', 'NOW', 'PANW', 'SNOW', 'DDOG', 'ZM', 'OKTA', 'CRWD', 'NET', 'PLTR',
            'RBLX', 'COIN', 'HOOD', 'SOFI', 'SQ', 'PYPL', 'SHOP', 'UBER', 'LYFT', 'ROKU',
            
            # Financial (35 stocks)
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'USB', 'PNC', 'TFC', 'COF',
            'AXP', 'BLK', 'SCHW', 'CB', 'MMC', 'AON', 'SPGI', 'ICE', 'CME', 'MCO',
            'V', 'MA', 'FIS', 'FISV', 'ADP', 'PAYX', 'TRV', 'PGR', 'ALL', 'MET',
            'PRU', 'AFL', 'AIG', 'HIG', 'CMA',
            
            # Healthcare (35 stocks)
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'ABT', 'DHR', 'BMY', 'LLY', 'MRK',
            'AMGN', 'GILD', 'MDLZ', 'CVS', 'CI', 'HUM', 'ANTM', 'SYK', 'BSX', 'EW',
            'ISRG', 'REGN', 'VRTX', 'BIIB', 'MRNA', 'ZTS', 'ILMN', 'IQV', 'A', 'DXCM',
            'ALGN', 'IDXX', 'MTD', 'BDX', 'BAX',
            
            # Consumer Discretionary (30 stocks)
            'HD', 'WMT', 'MCD', 'NKE', 'COST', 'SBUX', 'TGT', 'LOW', 'DIS', 'CMCSA',
            'BKNG', 'AMZN', 'TSLA', 'TJX', 'ORLY', 'AZO', 'ULTA', 'RCL', 'CCL', 'MAR',
            'HLT', 'MGM', 'WYNN', 'LVS', 'YUM', 'QSR', 'CMG', 'SBUX', 'DNKN', 'DPZ',
            
            # Consumer Staples (20 stocks)
            'PG', 'KO', 'PEP', 'WMT', 'COST', 'MDLZ', 'CL', 'KMB', 'GIS', 'K',
            'HSY', 'MKC', 'SJM', 'CPB', 'CAG', 'KHC', 'CHD', 'CLX', 'TSN', 'HRL',
            
            # Industrial (25 stocks)
            'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'RTX', 'LMT', 'NOC', 'GD',
            'DE', 'EMR', 'ETN', 'ITW', 'PH', 'CMI', 'FDX', 'WM', 'RSG', 'PCAR',
            'NSC', 'UNP', 'CSX', 'KSU', 'ODFL',
            
            # Energy & Materials (20 stocks)
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO', 'PSX', 'KMI', 'OKE',
            'LIN', 'APD', 'ECL', 'SHW', 'DD', 'DOW', 'NEM', 'FCX', 'GOLD', 'AA',
            
            # Utilities & REITs (20 stocks)
            'NEE', 'DUK', 'SO', 'D', 'EXC', 'XEL', 'SRE', 'AEP', 'ES', 'AWK',
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'EXR', 'AVB', 'EQR', 'UDR', 'ESS',
            
            # Communication Services (15 stocks)
            'GOOGL', 'META', 'NFLX', 'DIS', 'CMCSA', 'VZ', 'T', 'CHTR', 'TMUS', 'DISH',
            'TWTR', 'SNAP', 'PINS', 'MTCH', 'ZM'
        ]
    
    def _get_spy_data(self) -> pd.DataFrame:
        """Get SPY data for market regime analysis."""
        try:
            spy_data = self.data_manager.get_historical_data('SPY', self.start_date, self.end_date, use_cache=True)
            if not spy_data.empty:
                spy_data = self.indicators.add_all_indicators(spy_data)
            return spy_data
        except Exception as e:
            print(f"⚠️  Could not get SPY data: {e}")
            return pd.DataFrame()
    
    def backtest_strategy(self, signals: List[Dict], data: pd.DataFrame,
                         strategy_name: str) -> Dict:
        """
        Backtest the trend continuation strategy with given signals.
        
        Args:
            signals: List of signal dictionaries
            data: Stock price data
            strategy_name: Name of the strategy
            
        Returns:
            Dictionary with backtest results
        """
        if not signals:
            return {
                'strategy_name': strategy_name,
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'trades': []
            }
        
        trades = []
        total_return = 0
        winning_trades = 0
        total_profit = 0
        total_loss = 0
        
        for signal in signals:
            try:
                entry_date = signal['date']  # Don't convert - already correct type
                entry_price = signal['entry_price']
                max_hold_days = signal.get('max_hold_days', 15)
                profit_target = signal.get('profit_target', 8.0)
                stop_loss_pct = signal.get('stop_loss_pct', 3.0)

                # Find entry index
                entry_idx = data[data['date'] == entry_date].index
                if len(entry_idx) == 0:
                    continue

                entry_idx = entry_idx[0]

                # Initialize exit tracking
                exit_idx = entry_idx
                exit_price = entry_price
                exit_reason = 'No Exit'
                peak_price = entry_price  # Track highest price for metrics

                # Check each day for NEW exit conditions
                for day in range(entry_idx + 1, min(entry_idx + max_hold_days + 1, len(data))):
                    if day >= len(data):
                        break

                    daily_data = data.iloc[day]
                    current_price = daily_data['close']
                    current_high = daily_data['high']
                    current_low = daily_data['low']
                    current_ema20 = daily_data.get('ema_20', current_price)

                    # Update peak price for metrics
                    if current_high > peak_price:
                        peak_price = current_high

                    # NEW Exit Condition 1: Gain ≥ +8%
                    gain_pct = ((current_price - entry_price) / entry_price) * 100
                    if gain_pct >= profit_target:
                        exit_idx = day
                        exit_price = current_price
                        exit_reason = f'+{profit_target}% Target'
                        break

                    # NEW Exit Condition 2: Loss ≥ -3% (stop loss)
                    loss_pct = ((entry_price - current_low) / entry_price) * 100
                    if loss_pct >= stop_loss_pct:
                        exit_idx = day
                        exit_price = entry_price * (1 - stop_loss_pct / 100)  # Use stop loss price
                        exit_reason = f'-{stop_loss_pct}% Stop Loss'
                        break

                    # NEW Exit Condition 3: Close < EMA 20 (trailing exit)
                    if not pd.isna(current_ema20) and current_price < current_ema20:
                        exit_idx = day
                        exit_price = current_price
                        exit_reason = 'Below EMA 20'
                        break

                # NEW Exit Condition 4: 15 trading days have passed
                if exit_reason == 'No Exit':
                    final_day = min(entry_idx + max_hold_days, len(data) - 1)
                    exit_idx = final_day
                    exit_price = data.iloc[final_day]['close']
                    exit_reason = f'{max_hold_days}-Day Max Hold'
                
                # Calculate trade return
                trade_return = (exit_price - entry_price) / entry_price
                total_return += trade_return
                
                # Track wins/losses
                if trade_return > 0:
                    winning_trades += 1
                    total_profit += trade_return
                else:
                    total_loss += abs(trade_return)
                
                # Store trade details with trailing stop info
                trade = {
                    'entry_date': entry_date.strftime('%Y-%m-%d'),
                    'exit_date': data.iloc[exit_idx]['date'].strftime('%Y-%m-%d'),
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'peak_price': peak_price,
                    'return': trade_return,
                    'exit_reason': exit_reason,
                    'pattern_type': signal.get('pattern_type', 'Unknown'),
                    'signal_strength': signal.get('signal_strength', 50),
                    'hold_days': exit_idx - entry_idx,
                    'max_gain': ((peak_price - entry_price) / entry_price) * 100
                }
                trades.append(trade)
                
            except Exception as e:
                continue
        
        # Calculate performance metrics
        total_trades = len(trades)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        avg_return = (total_return / total_trades * 100) if total_trades > 0 else 0
        profit_factor = (total_profit / total_loss) if total_loss > 0 else float('inf') if total_profit > 0 else 0
        
        # Calculate max drawdown
        cumulative_returns = []
        cumulative = 0
        for trade in trades:
            cumulative += trade['return']
            cumulative_returns.append(cumulative)
        
        if cumulative_returns:
            peak = cumulative_returns[0]
            max_drawdown = 0
            for ret in cumulative_returns:
                if ret > peak:
                    peak = ret
                drawdown = (peak - ret) / (1 + peak) if peak != -1 else 0
                max_drawdown = max(max_drawdown, drawdown)
            max_drawdown *= 100
        else:
            max_drawdown = 0
        
        return {
            'strategy_name': strategy_name,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'profit_factor': profit_factor,
            'total_return': total_return * 100,
            'max_drawdown': max_drawdown,
            'trades': trades
        }

    def run_comprehensive_backtest(self) -> Dict:
        """
        Run comprehensive backtest across all stocks in the universe.

        Returns:
            Dictionary with complete backtest results
        """
        print("🚀 Starting Trend Continuation Strategy Comprehensive Backtest")
        print(f"📊 Testing {len(self.stock_universe)} stocks over 5 years ({self.start_date} to {self.end_date})")
        print(f"🎯 Target: 60-70% win rate with statistical significance")
        print("=" * 80)

        # Get SPY data for market regime analysis
        print("📈 Loading SPY data for market regime analysis...")
        spy_data = self._get_spy_data()

        all_signals = []
        processed_stocks = 0
        failed_stocks = 0

        for i, ticker in enumerate(self.stock_universe):
            try:
                if i % 25 == 0:
                    print(f"   Progress: {i}/{len(self.stock_universe)} stocks processed")

                # Get stock data (with caching enabled)
                data = self.data_manager.get_historical_data(ticker, self.start_date, self.end_date, use_cache=True)

                if data.empty or len(data) < 100:
                    failed_stocks += 1
                    continue

                # Add all technical indicators
                data = self.indicators.add_all_indicators(data)

                # Generate signals using the strategy
                signals = self.strategy.generate_signals(data, spy_data)

                # Add ticker to signals
                for signal in signals:
                    signal['ticker'] = ticker

                all_signals.extend(signals)
                processed_stocks += 1

            except Exception as e:
                print(f"   ⚠️  Error processing {ticker}: {e}")
                failed_stocks += 1
                continue

        print(f"\n✅ Data Processing Complete:")
        print(f"   📊 Processed: {processed_stocks} stocks")
        print(f"   ❌ Failed: {failed_stocks} stocks")
        print(f"   🎯 Total Signals: {len(all_signals)}")

        if not all_signals:
            print("❌ No signals found! Strategy may be too restrictive.")
            return {'error': 'No signals generated'}

        # Group signals by ticker for backtesting
        print(f"\n🧪 Running backtest on {len(all_signals)} signals...")
        ticker_results = []

        for ticker in set(signal['ticker'] for signal in all_signals):
            ticker_signals = [s for s in all_signals if s['ticker'] == ticker]

            try:
                data = self.data_manager.get_historical_data(ticker, self.start_date, self.end_date)
                data = self.indicators.add_all_indicators(data)

                result = self.backtest_strategy(ticker_signals, data, f"TrendContinuation_{ticker}")
                if result['total_trades'] > 0:
                    ticker_results.append(result)

            except Exception as e:
                continue

        # Aggregate results
        return self._aggregate_results(ticker_results, all_signals)

    def _aggregate_results(self, ticker_results: List[Dict], all_signals: List[Dict]) -> Dict:
        """
        Aggregate results from all tickers into comprehensive analysis.

        Args:
            ticker_results: List of individual ticker results
            all_signals: List of all signals generated

        Returns:
            Dictionary with aggregated results and analysis
        """
        if not ticker_results:
            return {'error': 'No successful backtests'}

        # Aggregate all trades
        all_trades = []
        for result in ticker_results:
            all_trades.extend(result['trades'])

        total_trades = len(all_trades)
        if total_trades == 0:
            return {'error': 'No trades executed'}

        # Calculate overall performance
        winning_trades = sum(1 for trade in all_trades if trade['return'] > 0)
        win_rate = (winning_trades / total_trades) * 100

        total_return = sum(trade['return'] for trade in all_trades)
        avg_return = (total_return / total_trades) * 100

        total_profit = sum(trade['return'] for trade in all_trades if trade['return'] > 0)
        total_loss = sum(abs(trade['return']) for trade in all_trades if trade['return'] < 0)
        profit_factor = (total_profit / total_loss) if total_loss > 0 else float('inf')

        # Pattern analysis
        pattern_stats = {}
        for trade in all_trades:
            pattern = trade.get('pattern_type', 'Unknown')
            if pattern not in pattern_stats:
                pattern_stats[pattern] = {'trades': 0, 'wins': 0, 'total_return': 0}
            pattern_stats[pattern]['trades'] += 1
            if trade['return'] > 0:
                pattern_stats[pattern]['wins'] += 1
            pattern_stats[pattern]['total_return'] += trade['return']

        # Calculate pattern win rates (ensure JSON serializable)
        for pattern in pattern_stats:
            stats = pattern_stats[pattern]
            stats['trades'] = int(stats['trades'])
            stats['wins'] = int(stats['wins'])
            stats['win_rate'] = float((stats['wins'] / stats['trades']) * 100) if stats['trades'] > 0 else 0.0
            stats['avg_return'] = float((stats['total_return'] / stats['trades']) * 100) if stats['trades'] > 0 else 0.0
            stats['total_return'] = float(stats['total_return'])

        # Holding time histogram (convert keys to strings for JSON serialization)
        holding_times = [trade.get('hold_days', 0) for trade in all_trades]
        holding_histogram = {}
        for days in holding_times:
            holding_histogram[str(days)] = holding_histogram.get(str(days), 0) + 1

        # Best/worst tickers analysis (ensure JSON serializable)
        ticker_performance = {}
        for result in ticker_results:
            ticker = result['strategy_name'].split('_')[-1] if '_' in result['strategy_name'] else 'Unknown'
            if result['total_trades'] > 0:
                ticker_performance[ticker] = {
                    'trades': int(result['total_trades']),  # Convert to regular int
                    'win_rate': float(result['win_rate']),  # Convert to regular float
                    'avg_return': float(result['avg_return']),  # Convert to regular float
                    'total_return': float(result['total_return'])  # Convert to regular float
                }

        # Sort tickers by performance
        best_tickers = sorted(ticker_performance.items(),
                            key=lambda x: x[1]['win_rate'], reverse=True)[:5]
        worst_tickers = sorted(ticker_performance.items(),
                             key=lambda x: x[1]['win_rate'])[:5]

        # Statistical significance test (basic)
        statistical_significance = total_trades >= 100  # Need at least 100 trades

        # Performance vs targets
        meets_win_rate_target = win_rate >= 60.0
        meets_profit_factor_target = profit_factor >= 1.3

        # Create comprehensive results (ensure JSON serializable)
        results = {
            'strategy_name': 'Trend Continuation with Pullback',
            'backtest_period': f"{self.start_date} to {self.end_date}",
            'stocks_tested': int(len(self.stock_universe)),
            'total_signals': int(len(all_signals)),
            'total_trades': int(total_trades),
            'win_rate': float(round(win_rate, 1)),
            'avg_return': float(round(avg_return, 2)),
            'profit_factor': float(round(profit_factor, 2)),
            'total_return': float(round(total_return * 100, 1)),
            'statistical_significance': bool(statistical_significance),
            'meets_targets': {
                'win_rate_60_plus': bool(meets_win_rate_target),
                'profit_factor_1_3_plus': bool(meets_profit_factor_target),
                'min_100_trades': bool(statistical_significance)
            },
            'pattern_analysis': pattern_stats,
            'holding_time_histogram': holding_histogram,
            'best_tickers': dict(best_tickers),
            'worst_tickers': dict(worst_tickers),
            'performance_summary': {
                'excellent': bool(win_rate >= 70 and profit_factor >= 2.0),
                'good': bool(win_rate >= 60 and profit_factor >= 1.3),
                'acceptable': bool(win_rate >= 55 and profit_factor >= 1.1),
                'poor': bool(win_rate < 55 or profit_factor < 1.1)
            }
        }

        # Store detailed results
        self.results = results

        return results

    def save_results(self, filename: str = None) -> str:
        """
        Save backtest results to JSON file.

        Args:
            filename: Optional custom filename

        Returns:
            String path to saved file
        """
        if not self.results:
            raise ValueError("No results to save. Run backtest first.")

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trend_continuation_backtest_{timestamp}.json"

        filepath = os.path.join("results", filename)

        # Ensure results directory exists
        os.makedirs("results", exist_ok=True)

        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

        return filepath

    def print_results_summary(self):
        """Print a formatted summary of the backtest results."""
        if not self.results:
            print("❌ No results available. Run backtest first.")
            return

        results = self.results

        print("\n" + "=" * 80)
        print("🎯 TREND CONTINUATION STRATEGY - BACKTEST RESULTS")
        print("=" * 80)

        print(f"📊 Strategy: {results['strategy_name']}")
        print(f"📅 Period: {results['backtest_period']}")
        print(f"🏢 Stocks Tested: {results['stocks_tested']}")
        print(f"📡 Total Signals: {results['total_signals']}")
        print(f"💼 Total Trades: {results['total_trades']}")

        print(f"\n🏆 PERFORMANCE METRICS:")
        print(f"   Win Rate: {results['win_rate']}% (Target: 60-70%)")
        print(f"   Avg Return: {results['avg_return']}%")
        print(f"   Profit Factor: {results['profit_factor']} (Target: >1.3)")
        print(f"   Total Return: {results['total_return']}%")

        print(f"\n✅ TARGET ACHIEVEMENT:")
        targets = results['meets_targets']
        print(f"   Win Rate ≥60%: {'✅' if targets['win_rate_60_plus'] else '❌'}")
        print(f"   Profit Factor ≥1.3: {'✅' if targets['profit_factor_1_3_plus'] else '❌'}")
        print(f"   Min 100 Trades: {'✅' if targets['min_100_trades'] else '❌'}")
        print(f"   Statistical Significance: {'✅' if results['statistical_significance'] else '❌'}")

        print(f"\n📈 PATTERN ANALYSIS:")
        for pattern, stats in results['pattern_analysis'].items():
            print(f"   {pattern}: {stats['trades']} trades, {stats['win_rate']:.1f}% win rate, {stats['avg_return']:.2f}% avg return")

        print(f"\n⏱️  HOLDING TIME HISTOGRAM:")
        for days, count in sorted(results['holding_time_histogram'].items()):
            print(f"   {days} days: {count} trades")

        print(f"\n🏆 BEST PERFORMING TICKERS:")
        for ticker, stats in list(results['best_tickers'].items())[:5]:
            print(f"   {ticker}: {stats['win_rate']:.1f}% win rate, {stats['avg_return']:.2f}% avg return ({stats['trades']} trades)")

        print(f"\n📉 WORST PERFORMING TICKERS:")
        for ticker, stats in list(results['worst_tickers'].items())[:5]:
            print(f"   {ticker}: {stats['win_rate']:.1f}% win rate, {stats['avg_return']:.2f}% avg return ({stats['trades']} trades)")

        # Overall assessment
        perf = results['performance_summary']
        if perf['excellent']:
            assessment = "🌟 EXCELLENT - Exceeds all targets!"
        elif perf['good']:
            assessment = "✅ GOOD - Meets target criteria"
        elif perf['acceptable']:
            assessment = "⚠️  ACCEPTABLE - Below targets but usable"
        else:
            assessment = "❌ POOR - Does not meet minimum criteria"

        print(f"\n🎯 OVERALL ASSESSMENT: {assessment}")
        print("=" * 80)


def main():
    """Main execution function."""
    print("🚀 Trend Continuation with Pullback Strategy - Comprehensive Backtest")
    print("=" * 80)
    print("📋 Strategy Details (RELAXED EXIT - Option A):")
    print("   • SPY Regime: Only trade when SPY in confirmed uptrend (close>EMA50, EMA20>EMA50, EMA20 rising)")
    print("   • Entry: Stock EMA 20 > EMA 50 + EMA 20 rising + RSI 30-65 + volume 1.05x")
    print("   • Exit: +6% gain OR -5% stop OR below EMA 20 OR 20 days max (RELAXED)")
    print("   • Target: 60-70% win rate with statistical significance")
    print("=" * 80)

    # Initialize backtester
    backtester = TrendContinuationBacktester()

    print(f"📊 Research Scope:")
    print(f"   • Stock Universe: {len(backtester.stock_universe)} stocks")
    print(f"   • Time Period: {backtester.start_date} to {backtester.end_date}")
    print(f"   • Data Points: ~5 years of daily data")
    print(f"   • Expected Signals: 200+ (targeting statistical significance)")
    print()

    # Run the comprehensive backtest
    try:
        results = backtester.run_comprehensive_backtest()

        if 'error' in results:
            print(f"❌ Backtest failed: {results['error']}")
            return

        # Print results summary
        backtester.print_results_summary()

        # Save results
        saved_file = backtester.save_results()
        print(f"\n💾 Results saved to: {saved_file}")

        # Additional analysis and recommendations
        print(f"\n📝 ANALYSIS & RECOMMENDATIONS:")

        win_rate = results['win_rate']
        profit_factor = results['profit_factor']
        total_trades = results['total_trades']

        if win_rate >= 65 and profit_factor >= 1.5 and total_trades >= 100:
            print("🌟 RECOMMENDATION: IMPLEMENT STRATEGY")
            print("   This strategy shows excellent performance and statistical significance.")
            print("   Ready for live trading implementation.")
        elif win_rate >= 60 and profit_factor >= 1.3 and total_trades >= 50:
            print("✅ RECOMMENDATION: CONSIDER IMPLEMENTATION")
            print("   Strategy meets minimum targets. Consider paper trading first.")
        elif total_trades < 50:
            print("⚠️  RECOMMENDATION: INSUFFICIENT DATA")
            print("   Not enough trades for statistical significance. Consider:")
            print("   • Relaxing entry criteria")
            print("   • Expanding stock universe")
            print("   • Extending time period")
        else:
            print("❌ RECOMMENDATION: DO NOT IMPLEMENT")
            print("   Strategy does not meet performance targets. Consider:")
            print("   • Adjusting parameters (RSI zones, EMA periods)")
            print("   • Adding additional filters")
            print("   • Testing different patterns")

        # Pattern-specific recommendations
        if 'pattern_analysis' in results:
            print(f"\n📊 PATTERN PERFORMANCE:")
            best_pattern = None
            best_win_rate = 0

            for pattern, stats in results['pattern_analysis'].items():
                if stats['trades'] >= 10 and stats['win_rate'] > best_win_rate:
                    best_pattern = pattern
                    best_win_rate = stats['win_rate']

            if best_pattern:
                print(f"   🏆 Best Pattern: {best_pattern} ({best_win_rate:.1f}% win rate)")
                print(f"   💡 Consider focusing on {best_pattern} patterns for higher accuracy")

        print(f"\n🔄 NEXT STEPS:")
        if results.get('meets_targets', {}).get('win_rate_60_plus', False):
            print("   1. ✅ Implement live screening system")
            print("   2. ✅ Set up position tracking")
            print("   3. ✅ Begin paper trading")
            print("   4. ✅ Monitor performance vs backtest")
        else:
            print("   1. 🔧 Optimize strategy parameters")
            print("   2. 🔧 Test additional filters")
            print("   3. 🔧 Validate with out-of-sample data")
            print("   4. 🔧 Re-run backtest with improvements")

    except Exception as e:
        print(f"❌ Error during backtesting: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
