#!/usr/bin/env python3
"""
Trend Continuation with Pullback Strategy
=========================================

High-probability strategy that trades pullbacks in strong trends.

Strategy Logic:
- Trend: EMA 20 > EMA 50 (uptrend confirmation)
- Pullback: RSI 40-60 zone (supports pullback entries)
- Pattern: Bull flag, pennant, or falling wedge in uptrend
- Entry: Price bouncing off EMA 20/50 during trends
- Probability: 60-70% win rate with good filtering

Research Parameters:
- 200+ stocks, 5 years of data (2020-2025)
- Same 2-day hold, 2% stop loss rules
- Statistical significance testing
- Pattern recognition validation

Usage:
    from trend_continuation_strategy import TrendContinuationStrategy
    strategy = TrendContinuationStrategy()
    signals = strategy.generate_signals(data)
"""

import pandas as pd
from typing import List, Dict
import sys
import os

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
from src.data.data_manager import DataManager
from src.analysis.indicators import TechnicalIndicators

class TrendContinuationStrategy:
    """
    Trend Continuation with Pullback strategy implementation.
    
    This strategy identifies high-probability pullback entries in strong trends
    using EMA trend confirmation, RSI pullback zones, and pattern recognition.
    """
    
    def __init__(self):
        """Initialize the Trend Continuation strategy."""
        self.data_manager = DataManager(cache_enabled=True)  # Enable caching for testing
        self.indicators = TechnicalIndicators()
        
        # Strategy parameters (relaxed exit strategy - Option A)
        self.ema_fast = 20          # Fast EMA for trend
        self.ema_slow = 50          # Slow EMA for trend
        self.rsi_min = 30           # RSI zone minimum (widened)
        self.rsi_max = 65           # RSI zone maximum (widened)
        self.max_hold_days = 20     # Maximum hold period (20 trading days - increased)
        self.profit_target = 6.0    # Exit at +6% gain (reduced from 8%)
        self.stop_loss_pct = 5.0    # Exit at -5% loss (increased from 3%)
        self.use_spy_regime_filter = True  # Global SPY regime filter

        # Volume parameters (simplified)
        self.volume_threshold = 1.05 # Volume confirmation (1.05x 20-day average - much lower)
        
        # Expected performance targets
        self.target_win_rate = 65.0
        self.target_profit_factor = 1.5
        
    def detect_uptrend(self, data: pd.DataFrame, index: int) -> bool:
        """
        Detect if stock is in a strong uptrend with practical EMA slope requirement.

        Entry Criteria (OPTION 2 - More Practical):
        - EMA 20 > EMA 50
        - Only EMA 20 must be sloping upward (EMA20[0] > EMA20[1])
        - EMA 50 can be flat or rising (more realistic)
        - Price stays above EMA 20 during pullback window

        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check

        Returns:
            bool: True if in uptrend, False otherwise
        """
        if index < self.ema_slow or index < 20:
            return False

        try:
            current_row = data.iloc[index]
            prev_row = data.iloc[index-1]

            # Check for valid EMA data
            if pd.isna(current_row['ema_20']) or pd.isna(current_row['ema_50']):
                return False
            if pd.isna(prev_row['ema_20']) or pd.isna(prev_row['ema_50']):
                return False

            # Primary trend condition: EMA 20 > EMA 50
            ema_trend = current_row['ema_20'] > current_row['ema_50']

            # OPTION 2: Only EMA 20 must be sloping upward
            ema_20_slope_up = current_row['ema_20'] > prev_row['ema_20']
            # EMA 50 can be flat or rising (removed strict requirement)

            # BALANCED: Allow brief EMA 20 touches but not sustained breaks
            # Check for closes below EMA 20 (not just low touches)
            pullback_window = min(5, index)
            sustained_breaks = 0

            for i in range(pullback_window):
                check_idx = index - i
                if check_idx >= 0:
                    check_row = data.iloc[check_idx]
                    if not pd.isna(check_row['ema_20']) and check_row['close'] < check_row['ema_20']:
                        sustained_breaks += 1

            # Allow 1 brief close below EMA 20, but not more
            price_above_ema20 = sustained_breaks <= 1

            return ema_trend and ema_20_slope_up and price_above_ema20

        except Exception as e:
            return False
    
    def detect_pullback_zone(self, data: pd.DataFrame, index: int) -> bool:
        """
        Detect if stock is in pullback zone (RSI 40-60).
        
        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check
            
        Returns:
            bool: True if in pullback zone, False otherwise
        """
        if index < 14:  # Need enough data for RSI
            return False
            
        try:
            current_row = data.iloc[index]
            
            # RSI in pullback zone (not oversold, but pulled back from strength)
            if pd.isna(current_row['rsi']):
                return False
                
            rsi_in_zone = self.rsi_min <= current_row['rsi'] <= self.rsi_max
            
            # Additional pullback confirmation
            # 1. RSI was higher recently (coming down from strength)
            if index >= 19:
                recent_rsi_max = data.iloc[index-5:index]['rsi'].max()
                rsi_pullback = recent_rsi_max > current_row['rsi'] + 5  # RSI pulled back at least 5 points
            else:
                rsi_pullback = True
                
            # 2. Price pulled back from recent high
            if index >= 10:
                recent_high = data.iloc[index-5:index]['high'].max()
                current_pullback = (recent_high - current_row['close']) / recent_high
                price_pullback = 0.02 <= current_pullback <= 0.08  # 2-8% pullback
            else:
                price_pullback = True
                
            return rsi_in_zone and rsi_pullback and price_pullback

        except Exception as e:
            return False

    def detect_bull_flag_pattern(self, data: pd.DataFrame, index: int) -> bool:
        """
        Detect bull flag pattern during pullback.

        Bull flag: Strong move up, followed by slight downward consolidation.

        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check

        Returns:
            bool: True if bull flag pattern detected, False otherwise
        """
        if index < self.pattern_lookback + 5:
            return False

        try:
            # 1. Strong initial move (flagpole)
            flagpole_start = index - self.pattern_lookback
            flagpole_end = index - 5
            flagpole_data = data.iloc[flagpole_start:flagpole_end+1]

            if len(flagpole_data) < 3:
                return False

            flagpole_gain = (flagpole_data['close'].iloc[-1] - flagpole_data['close'].iloc[0]) / flagpole_data['close'].iloc[0]
            strong_flagpole = flagpole_gain > 0.05  # At least 5% move up

            # 2. Consolidation/pullback (flag)
            flag_data = data.iloc[flagpole_end:index+1]
            if len(flag_data) < 3:
                return False

            flag_high = flag_data['high'].max()
            flag_low = flag_data['low'].min()
            flag_range = (flag_high - flag_low) / flag_low

            # Flag should be relatively tight consolidation
            tight_consolidation = flag_range < 0.08  # Less than 8% range

            # 3. Slight downward bias in flag (pullback)
            flag_slope = (flag_data['close'].iloc[-1] - flag_data['close'].iloc[0]) / len(flag_data)
            downward_bias = flag_slope <= 0  # Flat or slightly down

            # 4. Volume pattern (high on flagpole, lower on flag)
            if 'volume_sma' in data.columns:
                flagpole_volume = flagpole_data['volume'].mean()
                flag_volume = flag_data['volume'].mean()
                volume_pattern = flag_volume < flagpole_volume * 0.8  # Flag volume 20% lower
            else:
                volume_pattern = True

            return strong_flagpole and tight_consolidation and downward_bias and volume_pattern

        except Exception as e:
            return False

    def detect_pennant_pattern(self, data: pd.DataFrame, index: int) -> bool:
        """
        Detect pennant pattern during pullback.

        Pennant: Strong move up, followed by converging triangle consolidation.

        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check

        Returns:
            bool: True if pennant pattern detected, False otherwise
        """
        if index < self.pattern_lookback + 3:
            return False

        try:
            # 1. Strong initial move (flagpole)
            flagpole_start = index - self.pattern_lookback
            flagpole_end = index - 4
            flagpole_data = data.iloc[flagpole_start:flagpole_end+1]

            if len(flagpole_data) < 3:
                return False

            flagpole_gain = (flagpole_data['close'].iloc[-1] - flagpole_data['close'].iloc[0]) / flagpole_data['close'].iloc[0]
            strong_flagpole = flagpole_gain > 0.04  # At least 4% move up

            # 2. Converging triangle (pennant)
            pennant_data = data.iloc[flagpole_end:index+1]
            if len(pennant_data) < 4:
                return False

            # Check for converging highs and lows
            highs = pennant_data['high'].values
            lows = pennant_data['low'].values

            # Simple convergence check: range getting smaller
            early_range = highs[:2].max() - lows[:2].min()
            late_range = highs[-2:].max() - lows[-2:].min()

            converging = late_range < early_range * 0.7  # Range compressed by 30%

            # 3. Relatively short duration (pennants are quick)
            short_duration = len(pennant_data) <= 7  # Max 7 days

            return strong_flagpole and converging and short_duration

        except Exception as e:
            return False

    def detect_falling_wedge_pattern(self, data: pd.DataFrame, index: int) -> bool:
        """
        Detect falling wedge pattern during pullback.

        Falling wedge: Downward sloping support and resistance that converge.

        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check

        Returns:
            bool: True if falling wedge pattern detected, False otherwise
        """
        if index < self.pattern_lookback:
            return False

        try:
            wedge_data = data.iloc[index-self.pattern_lookback:index+1]

            if len(wedge_data) < 6:
                return False

            # 1. Overall downward bias but converging
            highs = wedge_data['high'].values
            lows = wedge_data['low'].values

            # 2. Both highs and lows trending down but converging
            # Simple trend check: compare first half to second half
            mid_point = len(wedge_data) // 2

            early_high = highs[:mid_point].max()
            late_high = highs[mid_point:].max()
            early_low = lows[:mid_point].min()
            late_low = lows[mid_point:].min()

            # Highs trending down
            highs_declining = late_high < early_high

            # Lows trending down but less steep (convergence)
            lows_declining = late_low < early_low

            # Convergence: the decline in lows is less steep than decline in highs
            if early_high > early_low and late_high > late_low:
                early_range = early_high - early_low
                late_range = late_high - late_low
                converging = late_range < early_range * 0.8  # Range compressing
            else:
                converging = False

            # 3. Volume typically decreases in wedge
            if 'volume_sma' in data.columns:
                early_volume = wedge_data['volume'].iloc[:mid_point].mean()
                late_volume = wedge_data['volume'].iloc[mid_point:].mean()
                volume_declining = late_volume < early_volume * 0.9
            else:
                volume_declining = True

            return highs_declining and lows_declining and converging and volume_declining

        except Exception as e:
            return False

    def check_volume_confirmation(self, data: pd.DataFrame, index: int) -> bool:
        """
        Check for volume confirmation of the breakout signal.

        REFINED: Breakout candle must have volume > 1.3× the 20-day average volume

        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check

        Returns:
            bool: True if volume confirms the breakout, False otherwise
        """
        if index < 20 or 'volume_sma' not in data.columns:
            return True  # Skip volume check if no data

        try:
            current_row = data.iloc[index]

            # REFINED: Breakout candle volume must be > 1.3x the 20-day average
            if pd.isna(current_row['volume_sma']) or current_row['volume_sma'] <= 0:
                return True  # Skip if no valid volume SMA

            volume_breakout = current_row['volume'] > current_row['volume_sma'] * self.volume_threshold

            return volume_breakout

        except Exception as e:
            return True  # Default to True if volume check fails

    def check_spy_regime_filter(self, spy_data: pd.DataFrame, index: int) -> bool:
        """
        Check if SPY is in confirmed uptrend (Global Market Regime Filter).

        SPY Uptrend Definition:
        - SPY close > SPY EMA 50
        - AND SPY EMA 20 > SPY EMA 50
        - AND SPY EMA 20 today > SPY EMA 20 yesterday (slope check)

        This is applied as a precondition - skip ALL trades if not true.

        Args:
            spy_data (pd.DataFrame): SPY data with indicators
            index (int): Current index to check

        Returns:
            bool: True if SPY in confirmed uptrend, False otherwise
        """
        if not self.use_spy_regime_filter or spy_data.empty or index >= len(spy_data) or index < 50:
            return True  # Skip SPY check if disabled or no data

        try:
            spy_row = spy_data.iloc[index]
            spy_prev_row = spy_data.iloc[index-1] if index > 0 else spy_row

            # Check for valid EMA data
            if (pd.isna(spy_row.get('ema_20')) or pd.isna(spy_row.get('ema_50')) or
                pd.isna(spy_prev_row.get('ema_20'))):
                return True  # Skip if no EMA data

            # SPY Uptrend Conditions
            # 1. SPY close > SPY EMA 50
            spy_above_ema50 = spy_row['close'] > spy_row['ema_50']

            # 2. SPY EMA 20 > SPY EMA 50
            spy_ema_trend = spy_row['ema_20'] > spy_row['ema_50']

            # 3. SPY EMA 20 rising (today > yesterday)
            spy_ema20_rising = spy_row['ema_20'] > spy_prev_row['ema_20']

            # All conditions must be true for confirmed uptrend
            spy_confirmed_uptrend = spy_above_ema50 and spy_ema_trend and spy_ema20_rising

            return spy_confirmed_uptrend

        except Exception as e:
            return True  # Default to True if SPY check fails

    def generate_signals(self, data: pd.DataFrame, spy_data: pd.DataFrame = None) -> List[Dict]:
        """
        Generate trend continuation pullback signals.

        Args:
            data (pd.DataFrame): Stock data with technical indicators
            spy_data (pd.DataFrame): SPY data for market confirmation (optional)

        Returns:
            List[Dict]: List of signal dictionaries
        """
        signals = []

        if data.empty or len(data) < 60:  # Need enough data for indicators
            return signals

        # Ensure we have all required indicators
        required_indicators = ['ema_20', 'ema_50', 'rsi', 'volume_sma']
        missing_indicators = [ind for ind in required_indicators if ind not in data.columns]

        if missing_indicators:
            print(f"⚠️  Missing indicators: {missing_indicators}")
            return signals

        # Scan for signals (skip first 60 days for indicator stability)
        for i in range(60, len(data)):
            try:
                current_row = data.iloc[i]

                # GLOBAL PRECONDITION: SPY Regime Filter (skip all trades if not met)
                if spy_data is not None and not self.check_spy_regime_filter(spy_data, i):
                    continue

                # 1. Check uptrend condition
                if not self.detect_uptrend(data, i):
                    continue

                # 2. Check RSI zone (widened to 30-65)
                if not self.detect_pullback_zone(data, i):
                    continue

                # 3. Volume confirmation (lowered to 1.05x)
                if not self.check_volume_confirmation(data, i):
                    continue

                # Pattern recognition removed for now
                pattern_type = "Trend Continuation"
                pattern_detected = True

                # Generate signal with new exit strategy
                signal = {
                    'date': current_row['date'],
                    'entry_price': current_row['close'],
                    'rsi': current_row['rsi'],
                    'ema_20': current_row['ema_20'],
                    'ema_50': current_row['ema_50'],
                    'volume_ratio': current_row['volume'] / current_row['volume_sma'] if current_row['volume_sma'] > 0 else 1.0,
                    'pattern_type': pattern_type,
                    'pattern_detected': pattern_detected,
                    'max_hold_days': self.max_hold_days,
                    'profit_target': self.profit_target,
                    'stop_loss_pct': self.stop_loss_pct,
                    'signal_strength': self._calculate_signal_strength(data, i, pattern_detected)
                }

                signals.append(signal)

            except Exception as e:
                continue

        return signals

    def _calculate_signal_strength(self, data: pd.DataFrame, index: int, pattern_detected: bool) -> float:
        """
        Calculate signal strength score (0-100).

        Args:
            data (pd.DataFrame): Stock data
            index (int): Current index
            pattern_detected (bool): Whether pattern was detected

        Returns:
            float: Signal strength score
        """
        try:
            current_row = data.iloc[index]
            score = 0.0

            # Base score for meeting criteria
            score += 30.0

            # EMA trend strength (20 points max)
            ema_gap = (current_row['ema_20'] - current_row['ema_50']) / current_row['ema_50']
            score += min(20.0, ema_gap * 1000)  # Scale appropriately

            # RSI position in zone (15 points max)
            rsi_position = (current_row['rsi'] - self.rsi_min) / (self.rsi_max - self.rsi_min)
            score += 15.0 * (1 - abs(rsi_position - 0.5) * 2)  # Best at middle of zone

            # Volume confirmation (15 points max)
            if 'volume_sma' in data.columns and current_row['volume_sma'] > 0:
                volume_ratio = current_row['volume'] / current_row['volume_sma']
                score += min(15.0, (volume_ratio - 1) * 10)

            # Pattern bonus (20 points max)
            if pattern_detected:
                score += 20.0

            return min(100.0, max(0.0, score))

        except Exception as e:
            return 50.0  # Default score
