# 🔬 RSI + Second Indicator Research Plan

## 🎯 Objective
Test if adding a second indicator to RSI < 25 can improve the 58.7% win rate while maintaining statistical significance.

## 📊 Current Baseline (RSI Only)
- **Win Rate**: 58.7%
- **Profit Factor**: 2.52
- **Average Return**: 2.03%
- **Research Base**: 5 years, 150 stocks, 5,256 trades
- **Statistical Significance**: P-value < 0.0001

## 🧪 Test Combinations

### 1. RSI + Volume Spike
- **Entry**: RSI < 25 AND Volume > 1.5x average
- **Hypothesis**: High volume confirms oversold bounce
- **Expected**: Higher win rate, fewer signals

### 2. RSI + MACD Bullish
- **Entry**: RSI < 25 AND MACD > Signal Line
- **Hypothesis**: Momentum confirmation improves timing
- **Expected**: Better entries, reduced false signals

### 3. RSI + Stochastic Oversold
- **Entry**: RSI < 25 AND Stochastic < 20
- **Hypothesis**: Double oversold = stronger bounce
- **Expected**: Higher win rate, much fewer signals

### 4. RSI + <PERSON>llinger Band Oversold
- **Entry**: RSI < 25 AND Price < BB Lower Band
- **Hypothesis**: Price + momentum oversold = strong reversal
- **Expected**: Improved win rate, moderate signal reduction

### 5. RSI + Moving Average Uptrend
- **Entry**: RSI < 25 AND Price > 20-day MA
- **Hypothesis**: Pullback in uptrend = better bounce
- **Expected**: Higher win rate, fewer signals

### 6. RSI + High ATR
- **Entry**: RSI < 25 AND ATR > 50th percentile
- **Hypothesis**: High volatility = bigger potential moves
- **Expected**: Higher average returns, similar win rate

### 7. RSI + SPY Bullish Regime
- **Entry**: RSI < 25 AND SPY > 50-day MA
- **Hypothesis**: Market tailwind improves individual stock bounces
- **Expected**: Higher win rate in bull markets

### 8. RSI + OBV Rising
- **Entry**: RSI < 25 AND OBV trending up
- **Hypothesis**: Volume momentum confirms price reversal
- **Expected**: Better signal quality

## 📈 Research Parameters

### Data Scope
- **Period**: June 10, 2020 - June 9, 2025 (5 years)
- **Stocks**: Same 150 stocks from original research
- **Market Conditions**: All regimes (bull, bear, neutral)

### Strategy Rules (Unchanged)
- **Hold Period**: Exactly 2 days
- **Stop Loss**: 2% maximum loss
- **Position Sizing**: Full capital deployment
- **Exit**: After 2 days OR stop loss hit

### Success Criteria
- **Minimum Trades**: 100+ for statistical significance
- **Win Rate Target**: > 60% (improvement over 58.7%)
- **Profit Factor**: > 2.5 (maintain or improve)
- **Max Drawdown**: < 20%

## 🎯 Expected Outcomes

### Likely Winners
1. **RSI + Volume Spike**: Volume confirms conviction
2. **RSI + SPY Bullish**: Market regime filter
3. **RSI + MA Uptrend**: Pullback in uptrend

### Potential Concerns
- **Signal Frequency**: Adding filters will reduce trade frequency
- **Overfitting**: More complex rules may not generalize
- **Market Regime Dependency**: Some combinations may only work in specific conditions

## 📊 Analysis Framework

### Performance Metrics
- Win Rate vs. Baseline (58.7%)
- Profit Factor vs. Baseline (2.52)
- Average Return vs. Baseline (2.03%)
- Total Trades (need 100+ minimum)
- Maximum Drawdown
- Statistical Significance

### Validation Approach
1. **Full Sample Testing**: All 5 years, 150 stocks
2. **Signal Quality**: Analyze signal characteristics
3. **Trade Frequency**: Ensure sufficient opportunities
4. **Robustness**: Check consistency across time periods

## 🚀 Implementation

### Quick Test (5 minutes)
```bash
python3 test_rsi_plus_indicators.py
```
- Tests framework with 10 stocks, 1.5 years
- Validates code works correctly
- Quick sanity check

### Full Analysis (30-45 minutes)
```bash
python3 rsi_plus_indicator_backtester.py
```
- Complete 150-stock, 5-year analysis
- All 8 indicator combinations
- Comprehensive statistical analysis
- Results saved to JSON

## 📋 Decision Framework

### Keep Strategy If:
- Win rate > 60% (meaningful improvement)
- Total trades > 100 (statistical significance)
- Profit factor > 2.0 (maintains profitability)
- Improvement is consistent across time periods

### Reject Strategy If:
- Win rate < 55% (worse than baseline)
- Total trades < 50 (insufficient data)
- Profit factor < 1.5 (poor risk/reward)
- High drawdown > 25%

## 🎯 Next Steps After Results

### If Improvement Found:
1. **Implement Best Strategy**: Update live screening
2. **Paper Trade**: Test with small positions first
3. **Monitor Performance**: Track vs. baseline
4. **Document Changes**: Update strategy documentation

### If No Improvement:
1. **Stick with RSI Only**: Proven 58.7% win rate
2. **Consider Other Approaches**: Different timeframes, exit rules
3. **Focus on Execution**: Perfect the current strategy

## 💡 Key Insights Expected

1. **Simplicity Often Wins**: Complex filters may not help
2. **Market Regime Matters**: SPY filter likely most effective
3. **Volume Confirms Quality**: Volume spike probably improves signals
4. **Trade-offs Exist**: Higher win rate = fewer opportunities

---

## 🚀 Ready to Run?

1. **Quick Test First**: `python3 test_rsi_plus_indicators.py`
2. **Full Analysis**: `python3 rsi_plus_indicator_backtester.py`
3. **Review Results**: Check which combinations beat 58.7%
4. **Implement Winner**: Update live trading system

**Remember**: The goal is meaningful improvement, not perfection. Even a 2-3% win rate improvement could be valuable if it maintains trade frequency and statistical significance.
