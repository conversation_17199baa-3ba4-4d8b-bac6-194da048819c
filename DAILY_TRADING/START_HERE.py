#!/usr/bin/env python3
"""
🎯 RSI TRADING SYSTEM - START HERE
==================================

Your daily trading workflow in one simple script.

Usage:
    python3 START_HERE.py

What it does:
    1. Shows today's RSI signals
    2. Helps you execute trades
    3. Tracks your performance
"""

import os
import sys
from datetime import datetime

def print_header():
    """Print the main header."""
    print("=" * 80)
    print("🎯 RSI OVERSOLD BOUNCE TRADING SYSTEM")
    print("=" * 80)
    print("📅 Date:", datetime.now().strftime('%A, %B %d, %Y'))
    print("💰 Portfolio: $813.65 USD")
    print("📊 Strategy: 58.7% Win Rate | 2.52 Profit Factor | 2.03% Avg Return")
    print("=" * 80)

def show_menu():
    """Show the main menu."""
    print("\n🚀 DAILY TRADING MENU:")
    print("=" * 50)
    print("1. 🔍 Find Today's RSI Signals (MAIN TOOL)")
    print("2. 📊 Track My Positions")
    print("3. 📈 Real-time Position Monitor")
    print("4. 🚨 Live RSI Alerts")
    print("5. 📋 View Recent Results")
    print("6. 📚 Help & Documentation")
    print("7. 🎯 Quick Trade Plan (OKTA)")
    print("0. ❌ Exit")
    print("=" * 50)

def run_enhanced_screener():
    """Run the enhanced daily screener."""
    print("\n🔍 RUNNING ENHANCED RSI SCREENER...")
    print("⏱️  This will take about 45 seconds to screen 100 stocks")
    print("🎯 Looking for RSI < 25 signals...")
    
    try:
        os.system("python3 enhanced_daily_screener.py")
    except Exception as e:
        print(f"❌ Error running screener: {e}")
        print("💡 Make sure you're in the DAILY_TRADING directory")

def run_position_tracker():
    """Run the position tracker."""
    print("\n📊 OPENING POSITION TRACKER...")
    try:
        os.system("python3 position_tracker.py")
    except Exception as e:
        print(f"❌ Error running position tracker: {e}")

def run_real_time_monitor():
    """Run real-time position monitor."""
    print("\n📈 STARTING REAL-TIME POSITION MONITOR...")
    print("💡 This will monitor your active positions with live WebSocket data")
    print("⚠️  Make sure you have active positions to monitor")
    
    try:
        os.system("python3 real_time_position_monitor.py")
    except Exception as e:
        print(f"❌ Error running real-time monitor: {e}")

def run_live_alerts():
    """Run live RSI alerts."""
    print("\n🚨 STARTING LIVE RSI ALERTS...")
    print("💡 This will continuously monitor for new RSI < 25 signals")
    print("⚠️  Leave this running during market hours")
    
    try:
        os.system("python3 live_rsi_alerts.py")
    except Exception as e:
        print(f"❌ Error running live alerts: {e}")

def show_recent_results():
    """Show recent results."""
    print("\n📋 RECENT RESULTS:")
    print("=" * 50)
    
    results_dir = "../results/daily_results"
    if os.path.exists(results_dir):
        files = sorted([f for f in os.listdir(results_dir) if f.endswith('.json')])
        if files:
            print("📁 Recent screening results:")
            for file in files[-5:]:  # Show last 5 files
                print(f"   📄 {file}")
        else:
            print("❌ No results found")
    else:
        print("❌ Results directory not found")
    
    print(f"\n📊 Latest Trade: MCD (RSI 12.3)")
    print(f"   Entry: Tuesday $301.19")
    print(f"   Exit: Friday (should have been Thursday)")
    print(f"   Lesson: Follow 2-day rule exactly")

def show_help():
    """Show help and documentation."""
    print("\n📚 HELP & DOCUMENTATION:")
    print("=" * 50)
    print("🎯 STRATEGY RULES:")
    print("   • Entry: RSI < 25 (oversold)")
    print("   • Hold: Exactly 2 days")
    print("   • Exit: Sell after 2 days regardless of price")
    print("   • Stop Loss: 2% maximum loss")
    print("")
    print("📁 IMPORTANT FILES:")
    print("   • enhanced_daily_screener.py - Find signals (MAIN TOOL)")
    print("   • position_tracker.py - Track your trades")
    print("   • ../results/daily_results/ - All your results")
    print("   • ../docs/ - Full documentation")
    print("")
    print("🚀 DAILY WORKFLOW:")
    print("   1. Run enhanced screener each morning")
    print("   2. Buy the strongest RSI signal")
    print("   3. Set 2% stop loss immediately")
    print("   4. Set calendar reminder to sell in 2 days")
    print("   5. Track results in position tracker")

def show_okta_plan():
    """Show OKTA trade plan."""
    print("\n🎯 OKTA TRADE PLAN (READY FOR MONDAY):")
    print("=" * 50)
    print("📊 Signal: RSI 15.1 (STRONG oversold)")
    print("💰 Entry: ~$102.00 (Monday market open)")
    print("📈 Shares: 8 shares (~$816 investment)")
    print("🛡️ Stop Loss: $99.96 (2% below entry)")
    print("📅 Exit: Wednesday (exactly 2 days)")
    print("🎯 Expected: 2.03% return (~$16.56 profit)")
    print("📊 Win Probability: 58.7%")
    print("")
    print("📋 EXECUTION PLAN:")
    print("   Monday 9:30 AM: Buy 8 shares OKTA")
    print("   Monday 9:31 AM: Set stop loss at $99.96")
    print("   Monday 9:32 AM: Set calendar reminder for Wednesday")
    print("   Wednesday: SELL (no exceptions!)")
    print("")
    print("💡 Remember: Follow the 2-day rule exactly!")

def main():
    """Main function."""
    # Change to DAILY_TRADING directory if not already there
    if not os.path.basename(os.getcwd()) == "DAILY_TRADING":
        if os.path.exists("DAILY_TRADING"):
            os.chdir("DAILY_TRADING")
        else:
            print("❌ Please run this from the SwingTrading directory")
            return
    
    print_header()
    
    while True:
        show_menu()
        
        try:
            choice = input("\n🎯 Choose an option (1-7, 0 to exit): ").strip()
            
            if choice == "0":
                print("\n👋 Happy trading! Remember: RSI < 25 → Buy → Hold 2 days → Sell")
                break
            elif choice == "1":
                run_enhanced_screener()
            elif choice == "2":
                run_position_tracker()
            elif choice == "3":
                run_real_time_monitor()
            elif choice == "4":
                run_live_alerts()
            elif choice == "5":
                show_recent_results()
            elif choice == "6":
                show_help()
            elif choice == "7":
                show_okta_plan()
            else:
                print("❌ Invalid choice. Please enter 1-7 or 0.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
