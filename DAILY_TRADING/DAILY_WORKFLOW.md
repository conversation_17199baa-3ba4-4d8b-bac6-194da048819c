# 📊 Daily RSI Trading Workflow

## 🌅 Morning Routine (9:00 AM)

### 1. Find Today's Signals
```bash
cd DAILY_TRADING
./run_screener.sh
```
**OR**
```bash
python3 START_HERE.py
# Choose option 1
```

**Result**: Get RSI < 25 signals in 45 seconds

### 2. Execute Best Trade
- **Buy** the strongest RSI signal at market open
- **Set** 2% stop loss immediately  
- **Set** calendar reminder to sell in exactly 2 days

---

## 📈 During Market Hours

### Monitor Positions (Optional)
```bash
python3 real_time_position_monitor.py    # Live P&L tracking
python3 live_rsi_alerts.py              # Background alerts
```

### Track Performance
```bash
python3 position_tracker.py             # Record trades
```

---

## 🎯 Current Trade Plan: OKTA

**Signal**: RSI 15.1 (STRONG oversold)  
**Plan**: Buy Monday, sell Wednesday  
**Entry**: ~$102.00 (8 shares)  
**Stop Loss**: $99.96 (2% below entry)  
**Expected**: 2.03% return (~$16.56 profit)  

### Monday Execution:
1. **9:30 AM**: Buy 8 shares OKTA at market open
2. **9:31 AM**: Set stop loss at $99.96
3. **9:32 AM**: Set calendar reminder for Wednesday
4. **Wednesday**: SELL (no exceptions!)

---

## 📋 Strategy Rules (Simple)

### Entry
- **RSI < 25** (oversold condition)
- **Any liquid stock** from screening

### Exit  
- **Hold exactly 2 days** (proven optimal)
- **2% stop loss** (risk management)

### That's It!
No other indicators, no complex analysis. Pure RSI oversold bounce.

---

## 📁 File Guide

### Main Tools (Use Daily)
- `enhanced_daily_screener.py` - Find signals (MAIN TOOL)
- `position_tracker.py` - Track your trades
- `START_HERE.py` - All-in-one menu
- `run_screener.sh` - Quick screener run

### Advanced Tools (Optional)
- `real_time_position_monitor.py` - Live monitoring
- `live_rsi_alerts.py` - Background alerts

### Results
- `../results/daily_results/` - All screening results
- `../results/daily_results/` - Trade journals

---

## 🎯 Quick Commands

### Find Signals
```bash
./run_screener.sh
```

### Track Positions  
```bash
python3 position_tracker.py
```

### All-in-One Menu
```bash
python3 START_HERE.py
```

---

## 📊 Recent Performance

### Latest Signals (Friday, June 13)
1. **OKTA**: RSI 15.1 (STRONG) - Ready for Monday
2. **KMB**: RSI 18.5 (STRONG)  
3. **K**: RSI 19.9 (STRONG)

### Recent Trade
- **MCD**: RSI 12.3 (VERY STRONG)
- **Entry**: Tuesday $301.19
- **Lesson**: Follow 2-day exit rule exactly

---

## 💡 Tips

1. **Keep it Simple**: RSI < 25 → Buy → Hold 2 days → Sell
2. **Follow Rules**: No exceptions to 2-day exit
3. **Set Reminders**: Calendar alerts for exit dates
4. **Track Results**: Use position tracker daily
5. **Trust Research**: 58.7% win rate over 5,256 trades

---

## 🆘 Need Help?

- **Can't find signals?** Run `./run_screener.sh`
- **Import errors?** Use `python3 START_HERE.py` instead
- **Results missing?** Check `../results/daily_results/`
- **Strategy questions?** Check `../docs/` folder

**Remember**: The strategy works because it's simple. Don't overthink it! 🚀
