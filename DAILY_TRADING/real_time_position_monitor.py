#!/usr/bin/env python3
"""
Real-time Position Monitor with WebSocket Integration
====================================================

Real-time monitoring of active RSI trading positions using Polygon WebSocket streams.

Features:
- Live price updates for active positions
- Real-time P&L calculation
- Instant stop-loss alerts
- 2-day exit reminders
- WebSocket-powered real-time data

Usage:
    python3 real_time_position_monitor.py
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from src.api.polygon_websocket import PolygonWebSocketClient
from position_tracker import PositionTracker

class RealTimePositionMonitor:
    """Real-time position monitor using WebSocket streams."""
    
    def __init__(self):
        """Initialize the real-time monitor."""
        self.ws_client = PolygonWebSocketClient()
        self.position_tracker = PositionTracker()
        self.active_positions = {}
        self.portfolio_value = 813.65  # USD
        
        # Performance tracking
        self.total_pnl = 0.0
        self.alerts_sent = []
        
        # Setup event handlers
        self._setup_handlers()

    def _setup_handlers(self):
        """Setup WebSocket event handlers."""
        self.ws_client.add_trade_handler(self._handle_trade_update)
        self.ws_client.add_quote_handler(self._handle_quote_update)
        self.ws_client.add_error_handler(self._handle_error)

    async def _handle_trade_update(self, trade_data: Dict):
        """Handle real-time trade updates."""
        ticker = trade_data.get('ticker')
        price = trade_data.get('price')
        timestamp = trade_data.get('timestamp')
        
        if ticker in self.active_positions and price:
            position = self.active_positions[ticker]
            
            # Update current price
            position['current_price'] = price
            position['last_update'] = datetime.now().isoformat()
            
            # Calculate real-time P&L
            entry_price = position['entry_price']
            shares = position['shares']
            current_value = price * shares
            entry_value = entry_price * shares
            pnl = current_value - entry_value
            pnl_percent = (pnl / entry_value) * 100
            
            position['current_pnl'] = pnl
            position['current_pnl_percent'] = pnl_percent
            
            # Check for stop-loss alert
            stop_loss_price = position['stop_loss_price']
            if price <= stop_loss_price:
                await self._send_stop_loss_alert(ticker, price, stop_loss_price, pnl)
            
            # Check for 2-day exit reminder
            await self._check_exit_reminder(ticker, position)
            
            # Display real-time update
            self._display_position_update(ticker, position, trade_data)

    async def _handle_quote_update(self, quote_data: Dict):
        """Handle real-time quote updates."""
        ticker = quote_data.get('ticker')
        bid = quote_data.get('bid')
        ask = quote_data.get('ask')
        
        if ticker in self.active_positions and bid and ask:
            position = self.active_positions[ticker]
            position['current_bid'] = bid
            position['current_ask'] = ask
            position['spread'] = ask - bid
            position['spread_percent'] = ((ask - bid) / ask) * 100

    async def _handle_error(self, error_data: Dict):
        """Handle WebSocket errors."""
        print(f"❌ WebSocket Error: {error_data.get('error', 'Unknown error')}")

    async def _send_stop_loss_alert(self, ticker: str, current_price: float, 
                                   stop_loss_price: float, pnl: float):
        """Send stop-loss alert."""
        alert_key = f"{ticker}_stop_loss"
        
        # Avoid duplicate alerts
        if alert_key in self.alerts_sent:
            return
        
        self.alerts_sent.append(alert_key)
        
        print("\n" + "🚨" * 20)
        print("🚨 STOP LOSS ALERT 🚨")
        print("🚨" * 20)
        print(f"Ticker: {ticker}")
        print(f"Current Price: ${current_price:.2f}")
        print(f"Stop Loss Price: ${stop_loss_price:.2f}")
        print(f"Current P&L: ${pnl:.2f}")
        print(f"Action Required: SELL IMMEDIATELY")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🚨" * 20)
        
        # Save alert to file
        await self._save_alert(ticker, "STOP_LOSS", {
            'current_price': current_price,
            'stop_loss_price': stop_loss_price,
            'pnl': pnl
        })

    async def _check_exit_reminder(self, ticker: str, position: Dict):
        """Check if it's time for 2-day exit reminder."""
        entry_date = datetime.fromisoformat(position['entry_date'])
        days_held = (datetime.now() - entry_date).days
        
        if days_held >= 2:
            alert_key = f"{ticker}_exit_reminder"
            
            if alert_key not in self.alerts_sent:
                self.alerts_sent.append(alert_key)
                await self._send_exit_reminder(ticker, position, days_held)

    async def _send_exit_reminder(self, ticker: str, position: Dict, days_held: int):
        """Send 2-day exit reminder."""
        print("\n" + "⏰" * 20)
        print("⏰ EXIT REMINDER ⏰")
        print("⏰" * 20)
        print(f"Ticker: {ticker}")
        print(f"Days Held: {days_held}")
        print(f"Strategy Rule: Exit after 2 days")
        print(f"Current P&L: ${position.get('current_pnl', 0):.2f}")
        print(f"Action Required: CONSIDER SELLING")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("⏰" * 20)
        
        # Save alert to file
        await self._save_alert(ticker, "EXIT_REMINDER", {
            'days_held': days_held,
            'current_pnl': position.get('current_pnl', 0)
        })

    async def _save_alert(self, ticker: str, alert_type: str, data: Dict):
        """Save alert to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        alert = {
            'timestamp': timestamp,
            'ticker': ticker,
            'alert_type': alert_type,
            'data': data
        }
        
        # Ensure daily_results directory exists
        os.makedirs('daily_results', exist_ok=True)
        
        filename = f"daily_results/alert_{ticker}_{alert_type}_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(alert, f, indent=2, default=str)

    def _display_position_update(self, ticker: str, position: Dict, trade_data: Dict):
        """Display real-time position update."""
        current_time = datetime.now().strftime('%H:%M:%S')
        
        print(f"\n📈 {current_time} | {ticker} | "
              f"${position['current_price']:.2f} | "
              f"P&L: ${position['current_pnl']:.2f} ({position['current_pnl_percent']:+.2f}%)")

    def load_active_positions(self) -> Dict:
        """Load active positions from position tracker."""
        try:
            positions = self.position_tracker.load_positions()
            active_positions = {}
            
            for position in positions:
                if position.get('status') == 'ACTIVE':
                    ticker = position['ticker']
                    active_positions[ticker] = position
            
            print(f"📊 Loaded {len(active_positions)} active positions")
            return active_positions
            
        except Exception as e:
            print(f"❌ Error loading positions: {str(e)}")
            return {}

    async def start_monitoring(self):
        """Start real-time position monitoring."""
        print("=" * 80)
        print("📊 REAL-TIME POSITION MONITOR")
        print("🚀 Powered by Polygon WebSocket")
        print("=" * 80)
        
        # Load active positions
        self.active_positions = self.load_active_positions()
        
        if not self.active_positions:
            print("❌ No active positions to monitor")
            print("💡 Use position_tracker.py to add positions first")
            return
        
        # Display active positions
        print(f"\n📋 MONITORING {len(self.active_positions)} ACTIVE POSITIONS:")
        for ticker, position in self.active_positions.items():
            entry_date = position['entry_date']
            entry_price = position['entry_price']
            shares = position['shares']
            stop_loss = position['stop_loss_price']
            
            print(f"   {ticker}: {shares} shares @ ${entry_price:.2f} | "
                  f"Stop: ${stop_loss:.2f} | Entry: {entry_date}")
        
        # Connect to WebSocket
        print(f"\n🔌 Connecting to Polygon WebSocket...")
        connected = await self.ws_client.connect()
        
        if not connected:
            print("❌ Failed to connect to WebSocket")
            return
        
        # Subscribe to real-time data for active positions
        tickers = list(self.active_positions.keys())
        print(f"📡 Subscribing to real-time data for: {', '.join(tickers)}")
        
        await self.ws_client.subscribe_to_trades(tickers)
        await self.ws_client.subscribe_to_quotes(tickers)
        
        print(f"\n✅ Real-time monitoring started!")
        print(f"🎯 Watching for stop-loss alerts and exit reminders")
        print(f"💡 Press Ctrl+C to stop monitoring")
        
        # Start listening for real-time updates
        try:
            await self.ws_client.listen()
        except KeyboardInterrupt:
            print(f"\n⏹️  Monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Monitoring error: {str(e)}")
        finally:
            await self.ws_client.disconnect()

    def display_portfolio_summary(self):
        """Display current portfolio summary."""
        if not self.active_positions:
            return
        
        total_value = 0
        total_pnl = 0
        
        print(f"\n📊 PORTFOLIO SUMMARY")
        print("-" * 50)
        
        for ticker, position in self.active_positions.items():
            current_price = position.get('current_price', position['entry_price'])
            shares = position['shares']
            entry_value = position['entry_price'] * shares
            current_value = current_price * shares
            pnl = current_value - entry_value
            
            total_value += current_value
            total_pnl += pnl
            
            print(f"{ticker}: ${current_value:.2f} | P&L: ${pnl:+.2f}")
        
        print("-" * 50)
        print(f"Total Value: ${total_value:.2f}")
        print(f"Total P&L: ${total_pnl:+.2f}")
        print(f"Portfolio Return: {(total_pnl / self.portfolio_value) * 100:+.2f}%")

async def main():
    """Run real-time position monitoring."""
    monitor = RealTimePositionMonitor()
    await monitor.start_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
