#!/usr/bin/env python3
"""
Debug version to identify which criteria are failing
"""

import sys
import os
import pandas as pd

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
from src.data.data_manager import DataManager
from src.analysis.indicators import TechnicalIndicators

def debug_criteria():
    """Debug each criteria individually to find the bottleneck."""
    print("🔍 DEBUGGING Trend Continuation V2 Criteria")
    print("=" * 60)
    
    data_manager = DataManager(cache_enabled=True)
    indicators = TechnicalIndicators()
    
    # Test with a few stocks
    test_stocks = ['AAPL', 'MSFT', 'GOOGL', 'NVDA', 'TSLA']
    
    for ticker in test_stocks:
        print(f"\n📈 Testing {ticker}...")
        
        try:
            # Get data
            data = data_manager.get_historical_data(ticker, '2020-01-01', '2025-06-09', use_cache=True)
            if data.empty:
                print(f"   ❌ No data for {ticker}")
                continue
                
            print(f"   ✅ Got {len(data)} rows of data")
            
            # Add indicators
            data = indicators.add_all_indicators(data)
            
            # Check for indicators
            required_cols = ['ema_20', 'ema_50', 'rsi', 'volume_sma', 'adx']
            missing = [col for col in required_cols if col not in data.columns]
            if missing:
                print(f"   ❌ Missing indicators: {missing}")
                continue
            print(f"   ✅ All indicators present")
            
            # Test each criteria individually
            total_days = len(data) - 60
            criteria_counts = {
                'ema_trend': 0,
                'ema_20_slope': 0,
                'ema_50_slope': 0,
                'rsi_zone': 0,
                'price_above_ema': 0,
                'volume_breakout': 0,
                'adx_condition': 0,
                'all_criteria': 0
            }
            
            for i in range(60, len(data)):
                try:
                    current = data.iloc[i]
                    prev = data.iloc[i-1]
                    prev2 = data.iloc[i-2]
                    
                    # Skip if any required data is NaN
                    if any(pd.isna(current[col]) for col in required_cols):
                        continue
                    if any(pd.isna(prev[col]) for col in ['ema_20', 'ema_50']):
                        continue
                    if any(pd.isna(prev2[col]) for col in ['ema_20', 'ema_50']):
                        continue
                    
                    # Test each criteria
                    # 1. EMA 20 > EMA 50
                    if current['ema_20'] > current['ema_50']:
                        criteria_counts['ema_trend'] += 1
                    
                    # 2. EMA 20 sloping upward (3 days)
                    if current['ema_20'] > prev['ema_20'] > prev2['ema_20']:
                        criteria_counts['ema_20_slope'] += 1
                    
                    # 3. EMA 50 sloping upward (3 days)
                    if current['ema_50'] > prev['ema_50'] > prev2['ema_50']:
                        criteria_counts['ema_50_slope'] += 1
                    
                    # 4. RSI 40-60
                    if 40 <= current['rsi'] <= 60:
                        criteria_counts['rsi_zone'] += 1
                    
                    # 5. Price above EMA 20 (simplified - just current)
                    if current['close'] > current['ema_20']:
                        criteria_counts['price_above_ema'] += 1
                    
                    # 6. Volume > 1.3x average
                    if current['volume'] > current['volume_sma'] * 1.3:
                        criteria_counts['volume_breakout'] += 1
                    
                    # 7. ADX > 20
                    if current['adx'] > 20:
                        criteria_counts['adx_condition'] += 1
                    
                    # 8. All criteria combined
                    if (current['ema_20'] > current['ema_50'] and
                        current['ema_20'] > prev['ema_20'] > prev2['ema_20'] and
                        current['ema_50'] > prev['ema_50'] > prev2['ema_50'] and
                        40 <= current['rsi'] <= 60 and
                        current['close'] > current['ema_20'] and
                        current['volume'] > current['volume_sma'] * 1.3 and
                        current['adx'] > 20):
                        criteria_counts['all_criteria'] += 1
                        
                except Exception as e:
                    continue
            
            # Print results
            print(f"   📊 Criteria Analysis (out of {total_days} days):")
            for criteria, count in criteria_counts.items():
                percentage = (count / total_days) * 100 if total_days > 0 else 0
                print(f"      {criteria}: {count} days ({percentage:.1f}%)")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🔍 DIAGNOSIS:")
    print("   Look for criteria with very low percentages (<1%)")
    print("   These are likely the bottlenecks causing zero signals")

if __name__ == "__main__":
    debug_criteria()
