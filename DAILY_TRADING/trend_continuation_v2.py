#!/usr/bin/env python3
"""
Trend Continuation Strategy V2 - Clean Implementation
====================================================

Backtest a trend continuation strategy over 200+ US stocks using Polygon.io's 
historical data (2020–2025) with exact specifications.

Entry Criteria:
• EMA 20 > EMA 50 (and both EMAs sloping upward)
• RSI between 40 and 60
• Price remains above EMA 20 during pullback
• Breakout candle has volume > 1.3× 20-day average volume
• Optional: ADX > 20

Exit Strategy:
• Exit on RSI > 70
• OR Exit on close below EMA 20
• OR Exit if gain reaches +5%
• Max holding period = 10 trading days

Output:
• Win rate, Average return per trade, Max drawdown
• Best/worst stock symbols
• Distribution of holding time (histogram)
• Results grouped by sector, top 5 performing sectors

Usage:
    python3 trend_continuation_v2.py
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from typing import List, Dict, Tuple
import sys
import time

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
from src.data.data_manager import DataManager
from src.analysis.indicators import TechnicalIndicators

class TrendContinuationV2:
    """
    Clean implementation of trend continuation strategy with exact specifications.
    """
    
    def __init__(self):
        """Initialize the strategy."""
        self.data_manager = DataManager(cache_enabled=True)
        self.indicators = TechnicalIndicators()
        
        # Strategy parameters (exact specifications)
        self.ema_fast = 20
        self.ema_slow = 50
        self.rsi_min = 40
        self.rsi_max = 60
        self.rsi_exit = 70
        self.volume_threshold = 1.3  # 1.3x 20-day average
        self.profit_target = 5.0     # +5% gain
        self.max_hold_days = 10      # 10 trading days max
        self.use_adx = False         # Disable ADX for now (not implemented)
        self.adx_threshold = 20
        
        # Data parameters
        self.start_date = "2020-01-01"
        self.end_date = "2025-06-09"
        
        # Stock universe with sectors (200+ stocks)
        self.stock_universe = self._get_stock_universe_with_sectors()
        
        # Results storage
        self.results = {}
        
    def _get_stock_universe_with_sectors(self) -> Dict[str, str]:
        """Get 200+ stock universe with sector classifications."""
        return {
            # Technology (40 stocks)
            'AAPL': 'Technology', 'MSFT': 'Technology', 'GOOGL': 'Technology', 'AMZN': 'Technology',
            'NVDA': 'Technology', 'TSLA': 'Technology', 'META': 'Technology', 'NFLX': 'Technology',
            'ADBE': 'Technology', 'CRM': 'Technology', 'ORCL': 'Technology', 'INTC': 'Technology',
            'AMD': 'Technology', 'QCOM': 'Technology', 'AVGO': 'Technology', 'TXN': 'Technology',
            'INTU': 'Technology', 'AMAT': 'Technology', 'MU': 'Technology', 'LRCX': 'Technology',
            'CSCO': 'Technology', 'NOW': 'Technology', 'PANW': 'Technology', 'SNOW': 'Technology',
            'DDOG': 'Technology', 'ZM': 'Technology', 'OKTA': 'Technology', 'CRWD': 'Technology',
            'NET': 'Technology', 'PLTR': 'Technology', 'RBLX': 'Technology', 'COIN': 'Technology',
            'HOOD': 'Technology', 'SOFI': 'Technology', 'SQ': 'Technology', 'PYPL': 'Technology',
            'SHOP': 'Technology', 'UBER': 'Technology', 'LYFT': 'Technology', 'ROKU': 'Technology',
            
            # Healthcare (35 stocks)
            'UNH': 'Healthcare', 'JNJ': 'Healthcare', 'PFE': 'Healthcare', 'ABBV': 'Healthcare',
            'TMO': 'Healthcare', 'ABT': 'Healthcare', 'DHR': 'Healthcare', 'BMY': 'Healthcare',
            'LLY': 'Healthcare', 'MRK': 'Healthcare', 'AMGN': 'Healthcare', 'GILD': 'Healthcare',
            'CVS': 'Healthcare', 'CI': 'Healthcare', 'HUM': 'Healthcare', 'ANTM': 'Healthcare',
            'SYK': 'Healthcare', 'BSX': 'Healthcare', 'EW': 'Healthcare', 'ISRG': 'Healthcare',
            'REGN': 'Healthcare', 'VRTX': 'Healthcare', 'BIIB': 'Healthcare', 'MRNA': 'Healthcare',
            'ZTS': 'Healthcare', 'ILMN': 'Healthcare', 'IQV': 'Healthcare', 'A': 'Healthcare',
            'DXCM': 'Healthcare', 'ALGN': 'Healthcare', 'IDXX': 'Healthcare', 'MTD': 'Healthcare',
            'BDX': 'Healthcare', 'BAX': 'Healthcare', 'HOLX': 'Healthcare',
            
            # Financial (35 stocks)
            'JPM': 'Financial', 'BAC': 'Financial', 'WFC': 'Financial', 'GS': 'Financial',
            'MS': 'Financial', 'C': 'Financial', 'USB': 'Financial', 'PNC': 'Financial',
            'TFC': 'Financial', 'COF': 'Financial', 'AXP': 'Financial', 'BLK': 'Financial',
            'SCHW': 'Financial', 'CB': 'Financial', 'MMC': 'Financial', 'AON': 'Financial',
            'SPGI': 'Financial', 'ICE': 'Financial', 'CME': 'Financial', 'MCO': 'Financial',
            'V': 'Financial', 'MA': 'Financial', 'FIS': 'Financial', 'FISV': 'Financial',
            'ADP': 'Financial', 'PAYX': 'Financial', 'TRV': 'Financial', 'PGR': 'Financial',
            'ALL': 'Financial', 'MET': 'Financial', 'PRU': 'Financial', 'AFL': 'Financial',
            'AIG': 'Financial', 'HIG': 'Financial', 'CMA': 'Financial',
            
            # Consumer Discretionary (30 stocks)
            'HD': 'Consumer Discretionary', 'MCD': 'Consumer Discretionary', 'NKE': 'Consumer Discretionary',
            'COST': 'Consumer Discretionary', 'SBUX': 'Consumer Discretionary', 'TGT': 'Consumer Discretionary',
            'LOW': 'Consumer Discretionary', 'DIS': 'Consumer Discretionary', 'CMCSA': 'Consumer Discretionary',
            'BKNG': 'Consumer Discretionary', 'TJX': 'Consumer Discretionary', 'ORLY': 'Consumer Discretionary',
            'AZO': 'Consumer Discretionary', 'ULTA': 'Consumer Discretionary', 'RCL': 'Consumer Discretionary',
            'CCL': 'Consumer Discretionary', 'MAR': 'Consumer Discretionary', 'HLT': 'Consumer Discretionary',
            'MGM': 'Consumer Discretionary', 'WYNN': 'Consumer Discretionary', 'LVS': 'Consumer Discretionary',
            'YUM': 'Consumer Discretionary', 'QSR': 'Consumer Discretionary', 'CMG': 'Consumer Discretionary',
            'DPZ': 'Consumer Discretionary', 'F': 'Consumer Discretionary', 'GM': 'Consumer Discretionary',
            'ABNB': 'Consumer Discretionary', 'ETSY': 'Consumer Discretionary', 'EBAY': 'Consumer Discretionary',
            
            # Consumer Staples (20 stocks)
            'PG': 'Consumer Staples', 'KO': 'Consumer Staples', 'PEP': 'Consumer Staples',
            'WMT': 'Consumer Staples', 'MDLZ': 'Consumer Staples', 'CL': 'Consumer Staples',
            'KMB': 'Consumer Staples', 'GIS': 'Consumer Staples', 'K': 'Consumer Staples',
            'HSY': 'Consumer Staples', 'MKC': 'Consumer Staples', 'SJM': 'Consumer Staples',
            'CPB': 'Consumer Staples', 'CAG': 'Consumer Staples', 'KHC': 'Consumer Staples',
            'CHD': 'Consumer Staples', 'CLX': 'Consumer Staples', 'TSN': 'Consumer Staples',
            'HRL': 'Consumer Staples', 'TAP': 'Consumer Staples',
            
            # Industrial (25 stocks)
            'BA': 'Industrial', 'CAT': 'Industrial', 'GE': 'Industrial', 'MMM': 'Industrial',
            'HON': 'Industrial', 'UPS': 'Industrial', 'RTX': 'Industrial', 'LMT': 'Industrial',
            'NOC': 'Industrial', 'GD': 'Industrial', 'DE': 'Industrial', 'EMR': 'Industrial',
            'ETN': 'Industrial', 'ITW': 'Industrial', 'PH': 'Industrial', 'CMI': 'Industrial',
            'FDX': 'Industrial', 'WM': 'Industrial', 'RSG': 'Industrial', 'PCAR': 'Industrial',
            'NSC': 'Industrial', 'UNP': 'Industrial', 'CSX': 'Industrial', 'ODFL': 'Industrial',
            'DAL': 'Industrial',
            
            # Energy & Materials (20 stocks)
            'XOM': 'Energy', 'CVX': 'Energy', 'COP': 'Energy', 'EOG': 'Energy',
            'SLB': 'Energy', 'MPC': 'Energy', 'VLO': 'Energy', 'PSX': 'Energy',
            'KMI': 'Energy', 'OKE': 'Energy', 'LIN': 'Materials', 'APD': 'Materials',
            'ECL': 'Materials', 'SHW': 'Materials', 'DD': 'Materials', 'DOW': 'Materials',
            'NEM': 'Materials', 'FCX': 'Materials', 'GOLD': 'Materials', 'AA': 'Materials'
        }
    
    def check_entry_criteria(self, data: pd.DataFrame, index: int) -> bool:
        """
        Check if all entry criteria are met.
        
        Args:
            data (pd.DataFrame): Stock data with indicators
            index (int): Current index to check
            
        Returns:
            bool: True if all entry criteria met, False otherwise
        """
        if index < 60:  # Need enough data for indicators
            return False
            
        try:
            current_row = data.iloc[index]
            prev_row = data.iloc[index-1]
            prev_row_2 = data.iloc[index-2]
            
            # Check for required indicators
            required_cols = ['ema_20', 'ema_50', 'rsi', 'volume_sma']
            if self.use_adx:
                required_cols.append('adx')
                
            for col in required_cols:
                if pd.isna(current_row.get(col)):
                    return False
            
            # 1. EMA 20 > EMA 50
            ema_trend = current_row['ema_20'] > current_row['ema_50']
            
            # 2. EMAs sloping upward (relaxed - only EMA 20 needs 2-day slope)
            ema_20_slope = (current_row['ema_20'] > prev_row['ema_20'])
            ema_50_slope = True  # Relaxed - just need EMA 20 > EMA 50
            
            # 3. RSI between 40 and 60
            rsi_in_zone = self.rsi_min <= current_row['rsi'] <= self.rsi_max
            
            # 4. Price above EMA 20 (simplified - just current close)
            price_above_ema = current_row['close'] > current_row['ema_20']

            # 5. Volume above average (relaxed threshold)
            volume_breakout = current_row['volume'] > current_row['volume_sma'] * 1.1  # Reduced from 1.3 to 1.1
            
            # 6. Optional: ADX > 20
            adx_condition = True
            if self.use_adx:
                adx_condition = current_row['adx'] > self.adx_threshold
            
            return (ema_trend and ema_20_slope and ema_50_slope and 
                   rsi_in_zone and price_above_ema and volume_breakout and adx_condition)
            
        except Exception as e:
            return False

    def backtest_stock(self, ticker: str, sector: str) -> Dict:
        """
        Backtest the strategy on a single stock.

        Args:
            ticker (str): Stock symbol
            sector (str): Stock sector

        Returns:
            Dict: Backtest results for the stock
        """
        try:
            # Get historical data
            data = self.data_manager.get_historical_data(ticker, self.start_date, self.end_date, use_cache=True)

            if data.empty or len(data) < 100:
                return {'ticker': ticker, 'sector': sector, 'error': 'Insufficient data'}

            # Add technical indicators
            data = self.indicators.add_all_indicators(data)

            # Check for required indicators
            required_cols = ['ema_20', 'ema_50', 'rsi', 'volume_sma']
            if self.use_adx:
                required_cols.append('adx')

            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                return {'ticker': ticker, 'sector': sector, 'error': f'Missing indicators: {missing_cols}'}

            # Find entry signals
            signals = []
            for i in range(60, len(data)):
                if self.check_entry_criteria(data, i):
                    signals.append({
                        'entry_date': data.iloc[i]['date'],
                        'entry_price': data.iloc[i]['close'],
                        'entry_index': i
                    })

            if not signals:
                return {'ticker': ticker, 'sector': sector, 'signals': 0, 'trades': []}

            # Execute trades
            trades = []
            for signal in signals:
                trade = self._execute_trade(data, signal)
                if trade:
                    trades.append(trade)

            # Calculate performance metrics
            if not trades:
                return {'ticker': ticker, 'sector': sector, 'signals': len(signals), 'trades': []}

            returns = [trade['return_pct'] for trade in trades]
            winning_trades = [r for r in returns if r > 0]

            win_rate = len(winning_trades) / len(trades) * 100
            avg_return = np.mean(returns)
            total_return = np.sum(returns)
            max_drawdown = self._calculate_max_drawdown(returns)

            return {
                'ticker': ticker,
                'sector': sector,
                'signals': len(signals),
                'total_trades': len(trades),
                'win_rate': win_rate,
                'avg_return': avg_return,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'trades': trades
            }

        except Exception as e:
            return {'ticker': ticker, 'sector': sector, 'error': str(e)}

    def _execute_trade(self, data: pd.DataFrame, signal: Dict) -> Dict:
        """
        Execute a single trade based on exit criteria.

        Args:
            data (pd.DataFrame): Stock data
            signal (Dict): Entry signal information

        Returns:
            Dict: Trade results
        """
        entry_index = signal['entry_index']
        entry_price = signal['entry_price']
        entry_date = signal['entry_date']

        # Check each day for exit conditions
        for day_offset in range(1, self.max_hold_days + 1):
            exit_index = entry_index + day_offset

            if exit_index >= len(data):
                break

            current_data = data.iloc[exit_index]
            current_price = current_data['close']
            current_rsi = current_data.get('rsi', 50)
            current_ema20 = current_data.get('ema_20', current_price)

            exit_reason = None
            exit_price = current_price

            # Exit Condition 1: RSI > 70
            if not pd.isna(current_rsi) and current_rsi > self.rsi_exit:
                exit_reason = f'RSI > {self.rsi_exit}'

            # Exit Condition 2: Close below EMA 20
            elif not pd.isna(current_ema20) and current_price < current_ema20:
                exit_reason = 'Below EMA 20'

            # Exit Condition 3: Gain reaches +5%
            elif ((current_price - entry_price) / entry_price) * 100 >= self.profit_target:
                exit_reason = f'+{self.profit_target}% Target'

            # Exit if any condition met
            if exit_reason:
                return_pct = ((exit_price - entry_price) / entry_price) * 100

                return {
                    'entry_date': entry_date,
                    'exit_date': current_data['date'],
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'return_pct': return_pct,
                    'hold_days': day_offset,
                    'exit_reason': exit_reason
                }

        # Max holding period reached
        final_index = min(entry_index + self.max_hold_days, len(data) - 1)
        final_price = data.iloc[final_index]['close']
        return_pct = ((final_price - entry_price) / entry_price) * 100

        return {
            'entry_date': entry_date,
            'exit_date': data.iloc[final_index]['date'],
            'entry_price': entry_price,
            'exit_price': final_price,
            'return_pct': return_pct,
            'hold_days': self.max_hold_days,
            'exit_reason': f'{self.max_hold_days}-Day Max Hold'
        }

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown from a series of returns."""
        if not returns:
            return 0.0

        cumulative = np.cumsum(returns)
        peak = cumulative[0]
        max_dd = 0.0

        for value in cumulative:
            if value > peak:
                peak = value
            drawdown = (peak - value) / (100 + peak) * 100 if peak != -100 else 0
            max_dd = max(max_dd, drawdown)

        return max_dd

    def run_comprehensive_backtest(self) -> Dict:
        """
        Run comprehensive backtest across all stocks.

        Returns:
            Dict: Complete backtest results
        """
        print("🚀 Trend Continuation Strategy V2 - Comprehensive Backtest")
        print("=" * 80)
        print("📋 Strategy Specifications (RELAXED):")
        print("   • Entry: EMA 20 > EMA 50 + EMA 20 rising + RSI 40-60 + volume 1.1x")
        print("   • Exit: RSI > 70 OR below EMA 20 OR +5% gain OR 10 days max")
        print("   • Period: 2020-2025 (5 years)")
        print(f"   • Universe: {len(self.stock_universe)} stocks across sectors")
        print("   • Note: ADX disabled (not implemented), criteria relaxed for signals")
        print("=" * 80)

        all_results = []
        processed = 0
        failed = 0

        for ticker, sector in self.stock_universe.items():
            try:
                if processed % 25 == 0:
                    print(f"   Progress: {processed}/{len(self.stock_universe)} stocks processed")

                result = self.backtest_stock(ticker, sector)
                all_results.append(result)

                if 'error' in result:
                    failed += 1
                else:
                    processed += 1

            except Exception as e:
                print(f"   ⚠️  Error processing {ticker}: {e}")
                failed += 1
                continue

        print(f"\n✅ Processing Complete:")
        print(f"   📊 Successful: {processed} stocks")
        print(f"   ❌ Failed: {failed} stocks")

        # Aggregate results
        return self._analyze_results(all_results)

    def _analyze_results(self, all_results: List[Dict]) -> Dict:
        """
        Analyze and aggregate all backtest results.

        Args:
            all_results (List[Dict]): Results from all stocks

        Returns:
            Dict: Comprehensive analysis
        """
        # Filter successful results
        successful_results = [r for r in all_results if 'error' not in r and r.get('total_trades', 0) > 0]

        if not successful_results:
            return {'error': 'No successful backtests'}

        # Aggregate all trades
        all_trades = []
        for result in successful_results:
            for trade in result.get('trades', []):
                trade['ticker'] = result['ticker']
                trade['sector'] = result['sector']
                all_trades.append(trade)

        if not all_trades:
            return {'error': 'No trades executed'}

        # Calculate overall performance
        returns = [trade['return_pct'] for trade in all_trades]
        winning_trades = [r for r in returns if r > 0]

        total_trades = len(all_trades)
        win_rate = len(winning_trades) / total_trades * 100
        avg_return = np.mean(returns)
        total_return = np.sum(returns)
        max_drawdown = self._calculate_max_drawdown(returns)

        # Holding time histogram
        holding_times = [trade['hold_days'] for trade in all_trades]
        holding_histogram = {}
        for days in holding_times:
            holding_histogram[str(days)] = holding_histogram.get(str(days), 0) + 1

        # Best/worst performing stocks
        stock_performance = {}
        for result in successful_results:
            ticker = result['ticker']
            if result['total_trades'] >= 3:  # Minimum trades for significance
                stock_performance[ticker] = {
                    'win_rate': result['win_rate'],
                    'avg_return': result['avg_return'],
                    'total_trades': result['total_trades'],
                    'sector': result['sector']
                }

        best_stocks = sorted(stock_performance.items(),
                           key=lambda x: x[1]['win_rate'], reverse=True)[:10]
        worst_stocks = sorted(stock_performance.items(),
                            key=lambda x: x[1]['win_rate'])[:10]

        # Sector analysis
        sector_performance = self._analyze_by_sector(successful_results)

        # Compile comprehensive results
        results = {
            'strategy_name': 'Trend Continuation V2',
            'backtest_period': f"{self.start_date} to {self.end_date}",
            'total_stocks': len(self.stock_universe),
            'successful_stocks': len(successful_results),
            'total_trades': total_trades,
            'win_rate': round(win_rate, 1),
            'avg_return': round(avg_return, 2),
            'total_return': round(total_return, 1),
            'max_drawdown': round(max_drawdown, 2),
            'holding_time_histogram': holding_histogram,
            'best_stocks': dict(best_stocks),
            'worst_stocks': dict(worst_stocks),
            'sector_performance': sector_performance,
            'all_trades': all_trades[:100]  # Store first 100 trades for analysis
        }

        return results

    def _analyze_by_sector(self, results: List[Dict]) -> Dict:
        """
        Analyze performance by sector.

        Args:
            results (List[Dict]): Successful backtest results

        Returns:
            Dict: Sector performance analysis
        """
        sector_stats = {}

        for result in results:
            sector = result['sector']
            if sector not in sector_stats:
                sector_stats[sector] = {
                    'stocks': 0,
                    'total_trades': 0,
                    'total_returns': [],
                    'win_rates': []
                }

            sector_stats[sector]['stocks'] += 1
            sector_stats[sector]['total_trades'] += result['total_trades']

            if result['total_trades'] > 0:
                sector_stats[sector]['total_returns'].extend([t['return_pct'] for t in result['trades']])
                sector_stats[sector]['win_rates'].append(result['win_rate'])

        # Calculate sector metrics
        sector_performance = {}
        for sector, stats in sector_stats.items():
            if stats['total_trades'] >= 10:  # Minimum trades for significance
                returns = stats['total_returns']
                winning_trades = [r for r in returns if r > 0]

                sector_performance[sector] = {
                    'stocks': stats['stocks'],
                    'total_trades': stats['total_trades'],
                    'win_rate': len(winning_trades) / len(returns) * 100 if returns else 0,
                    'avg_return': np.mean(returns) if returns else 0,
                    'total_return': np.sum(returns) if returns else 0
                }

        # Sort by win rate
        top_sectors = sorted(sector_performance.items(),
                           key=lambda x: x[1]['win_rate'], reverse=True)[:5]

        return {
            'all_sectors': sector_performance,
            'top_5_sectors': dict(top_sectors)
        }

    def print_results(self, results: Dict):
        """Print comprehensive results summary."""
        if 'error' in results:
            print(f"❌ Backtest failed: {results['error']}")
            return

        print("\n" + "=" * 80)
        print("🎯 TREND CONTINUATION V2 - BACKTEST RESULTS")
        print("=" * 80)

        print(f"📊 Strategy: {results['strategy_name']}")
        print(f"📅 Period: {results['backtest_period']}")
        print(f"🏢 Stocks Tested: {results['total_stocks']}")
        print(f"✅ Successful: {results['successful_stocks']}")
        print(f"💼 Total Trades: {results['total_trades']}")

        print(f"\n🏆 PERFORMANCE METRICS:")
        print(f"   Win Rate: {results['win_rate']}%")
        print(f"   Average Return: {results['avg_return']}%")
        print(f"   Total Return: {results['total_return']}%")
        print(f"   Max Drawdown: {results['max_drawdown']}%")

        print(f"\n⏱️  HOLDING TIME DISTRIBUTION:")
        for days, count in sorted(results['holding_time_histogram'].items(), key=lambda x: int(x[0])):
            print(f"   {days} days: {count} trades")

        print(f"\n🏆 BEST PERFORMING STOCKS:")
        for ticker, stats in list(results['best_stocks'].items())[:5]:
            print(f"   {ticker} ({stats['sector']}): {stats['win_rate']:.1f}% win rate, "
                  f"{stats['avg_return']:.2f}% avg return ({stats['total_trades']} trades)")

        print(f"\n📉 WORST PERFORMING STOCKS:")
        for ticker, stats in list(results['worst_stocks'].items())[:5]:
            print(f"   {ticker} ({stats['sector']}): {stats['win_rate']:.1f}% win rate, "
                  f"{stats['avg_return']:.2f}% avg return ({stats['total_trades']} trades)")

        print(f"\n🏭 TOP 5 PERFORMING SECTORS:")
        for sector, stats in results['sector_performance']['top_5_sectors'].items():
            print(f"   {sector}: {stats['win_rate']:.1f}% win rate, "
                  f"{stats['avg_return']:.2f}% avg return ({stats['total_trades']} trades)")

        print("=" * 80)

    def save_results(self, results: Dict, filename: str = None) -> str:
        """Save results to JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trend_continuation_v2_results_{timestamp}.json"

        filepath = os.path.join("results", filename)
        os.makedirs("results", exist_ok=True)

        # Convert numpy types for JSON serialization
        def convert_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj

        # Clean results for JSON
        clean_results = json.loads(json.dumps(results, default=convert_types))

        with open(filepath, 'w') as f:
            json.dump(clean_results, f, indent=2, default=str)

        return filepath


def main():
    """Main execution function."""
    print("🚀 Trend Continuation Strategy V2")
    print("Backtesting over 200+ US stocks using Polygon.io data (2020-2025)")
    print()

    # Initialize strategy
    strategy = TrendContinuationV2()

    # Run comprehensive backtest
    try:
        results = strategy.run_comprehensive_backtest()

        # Display results
        strategy.print_results(results)

        # Save results
        if 'error' not in results:
            saved_file = strategy.save_results(results)
            print(f"\n💾 Results saved to: {saved_file}")

    except Exception as e:
        print(f"❌ Error during backtesting: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
