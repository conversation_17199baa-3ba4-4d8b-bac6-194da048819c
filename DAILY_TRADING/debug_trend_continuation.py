#!/usr/bin/env python3
"""
Debug version to find the actual error in trend continuation strategy
"""

import sys
import os
import traceback

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))

def debug_strategy():
    """Debug the strategy step by step to find the actual error."""
    print("🔍 DEBUGGING Trend Continuation Strategy")
    print("=" * 60)
    
    try:
        print("1. Testing imports...")
        from src.data.data_manager import DataManager
        from src.analysis.indicators import TechnicalIndicators
        from trend_continuation_strategy import TrendContinuationStrategy
        print("   ✅ Imports successful")
        
        print("2. Testing initialization...")
        data_manager = DataManager(cache_enabled=True)
        indicators = TechnicalIndicators()
        strategy = TrendContinuationStrategy()
        print("   ✅ Initialization successful")
        
        print("3. Testing data retrieval...")
        data = data_manager.get_historical_data('AAPL', '2023-01-01', '2025-06-09', use_cache=True)
        print(f"   ✅ Got {len(data)} rows of AAPL data")
        
        print("4. Testing indicators...")
        data = indicators.add_all_indicators(data)
        required_indicators = ['ema_20', 'ema_50', 'rsi', 'volume_sma']
        missing = [ind for ind in required_indicators if ind not in data.columns]
        if missing:
            print(f"   ❌ Missing indicators: {missing}")
            return
        print("   ✅ All indicators present")
        
        print("5. Testing SPY data...")
        spy_data = data_manager.get_historical_data('SPY', '2023-01-01', '2025-06-09', use_cache=True)
        spy_data = indicators.add_all_indicators(spy_data)
        print(f"   ✅ Got {len(spy_data)} rows of SPY data")
        
        print("6. Testing signal generation...")
        signals = strategy.generate_signals(data, spy_data)
        print(f"   ✅ Generated {len(signals)} signals")
        
        if len(signals) == 0:
            print("   ⚠️  No signals generated - checking criteria...")
            
            # Check SPY regime filter
            spy_uptrend_days = 0
            for i in range(60, len(spy_data)):
                if strategy.check_spy_regime_filter(spy_data, i):
                    spy_uptrend_days += 1
            print(f"   SPY uptrend days: {spy_uptrend_days}/{len(spy_data)-60}")
            
            # Check stock criteria
            uptrend_days = 0
            rsi_days = 0
            volume_days = 0
            
            for i in range(60, len(data)):
                if strategy.detect_uptrend(data, i):
                    uptrend_days += 1
                if strategy.detect_pullback_zone(data, i):
                    rsi_days += 1
                if strategy.check_volume_confirmation(data, i):
                    volume_days += 1
            
            print(f"   Stock uptrend days: {uptrend_days}/{len(data)-60}")
            print(f"   RSI 30-65 days: {rsi_days}/{len(data)-60}")
            print(f"   Volume 1.05x days: {volume_days}/{len(data)-60}")
            
        print("7. Testing backtesting...")
        if signals:
            print(f"   Signal dates: {[s['date'] for s in signals[:3]]}")
            print(f"   Data date range: {data['date'].iloc[0]} to {data['date'].iloc[-1]}")
            print(f"   Data date types: {type(data['date'].iloc[0])}")
            print(f"   Signal date types: {type(signals[0]['date'])}")

            from trend_continuation_backtester import TrendContinuationBacktester
            backtester = TrendContinuationBacktester()
            result = backtester.backtest_strategy(signals, data, "Debug_AAPL")
            print(f"   ✅ Backtest result: {result['total_trades']} trades, {result['win_rate']:.1f}% win rate")

            if result['total_trades'] == 0:
                print("   🔍 Checking date matching...")
                for signal in signals[:2]:
                    entry_date = signal['date']
                    matches = data[data['date'] == entry_date]
                    print(f"   Signal date {entry_date} matches: {len(matches)} rows")
        else:
            print("   ⚠️  No signals to backtest")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print("Full traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    debug_strategy()
