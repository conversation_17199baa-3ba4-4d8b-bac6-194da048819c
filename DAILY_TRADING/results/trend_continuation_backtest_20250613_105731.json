{"strategy_name": "Trend Continuation with <PERSON><PERSON><PERSON>", "backtest_period": "2020-06-10 to 2025-06-09", "stocks_tested": 240, "total_signals": 2583, "total_trades": 2583, "win_rate": 29.7, "avg_return": 0.2, "profit_factor": 1.13, "total_return": 508.1, "statistical_significance": true, "meets_targets": {"win_rate_60_plus": false, "profit_factor_1_3_plus": false, "min_100_trades": true}, "pattern_analysis": {"Trend Continuation": {"trades": 2583, "wins": 767, "total_return": 5.08103630208807, "win_rate": 29.69415408439799, "avg_return": 0.1967106582302776}}, "holding_time_histogram": {"5": 151, "3": 265, "1": 651, "2": 384, "4": 207, "13": 50, "8": 115, "20": 58, "9": 81, "10": 78, "6": 150, "7": 122, "12": 45, "16": 31, "11": 69, "15": 36, "14": 35, "17": 16, "0": 2, "19": 16, "18": 21}, "best_tickers": {"COIN": {"trades": 1, "win_rate": 100.0, "avg_return": 9.008345606283743, "total_return": 9.008345606283743}, "BA": {"trades": 8, "win_rate": 87.5, "avg_return": 6.533082310486975, "total_return": 52.2646584838958}, "BAX": {"trades": 6, "win_rate": 83.33333333333334, "avg_return": 3.878881608047482, "total_return": 23.27328964828489}, "ADP": {"trades": 9, "win_rate": 77.77777777777779, "avg_return": 2.1029019897371133, "total_return": 18.92611790763402}, "PH": {"trades": 12, "win_rate": 75.0, "avg_return": 4.011791648326538, "total_return": 48.14149977991846}}, "worst_tickers": {"AZO": {"trades": 7, "win_rate": 0.0, "avg_return": -2.6649362100116565, "total_return": -18.654553470081595}, "PLD": {"trades": 7, "win_rate": 0.0, "avg_return": -1.7248734121079017, "total_return": -12.074113884755313}, "K": {"trades": 3, "win_rate": 0.0, "avg_return": -0.9812559768566956, "total_return": -2.943767930570087}, "MS": {"trades": 9, "win_rate": 0.0, "avg_return": -2.7222740263101928, "total_return": -24.500466236791734}, "PEP": {"trades": 4, "win_rate": 0.0, "avg_return": -1.2395193965459759, "total_return": -4.9580775861839035}}, "performance_summary": {"excellent": false, "good": false, "acceptable": false, "poor": true}}