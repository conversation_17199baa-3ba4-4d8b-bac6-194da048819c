#!/usr/bin/env python3
"""
RSI + Second Indicator Backtesting Framework
===========================================

Comprehensive testing of RSI < 25 combined with additional indicators
to find the best combination that improves the 58.7% win rate.

Test Combinations:
1. RSI + Volume Spike
2. RSI + MACD Bullish
3. RSI + Stochastic Oversold
4. RSI + Bollinger Band Position
5. RSI + Moving Average Trend
6. RSI + ATR Volatility
7. RSI + SPY Market Regime
8. RSI + OBV Momentum

Research Parameters:
- Same 150 stocks, 5 years of data
- Same 2-day hold, 2% stop loss rules
- Statistical significance testing
- Out-of-sample validation

Usage:
    python3 rsi_plus_indicator_backtester.py
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system', 'src'))

from data.data_manager import DataManager
from analysis.indicators import TechnicalIndicators
from api.polygon_client import PolygonClient

class RSIPlusIndicatorBacktester:
    """
    Comprehensive backtesting framework for RSI + second indicator combinations.
    """
    
    def __init__(self):
        """Initialize the backtester."""
        self.data_manager = DataManager()
        self.indicators = TechnicalIndicators()
        
        # Base RSI strategy parameters
        self.rsi_threshold = 25
        self.hold_days = 2
        self.stop_loss_pct = 2
        
        # Research parameters
        self.start_date = "2020-06-10"
        self.end_date = "2025-06-09"
        
        # Stock universe (same 150 stocks from original research)
        self.stock_universe = self._get_research_stock_universe()
        
        # Results storage
        self.results = {}
        
    def _get_research_stock_universe(self) -> List[str]:
        """Get the same 150 stocks used in original research."""
        # Top 150 liquid stocks across sectors
        return [
            # Technology
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'ADBE', 'CRM',
            'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN', 'INTU', 'AMAT', 'MU', 'LRCX',
            
            # Financial
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'USB', 'PNC', 'TFC', 'COF',
            'AXP', 'BLK', 'SCHW', 'CB', 'MMC', 'AON', 'SPGI', 'ICE', 'CME', 'MCO',
            
            # Healthcare
            'UNH', 'JNJ', 'PFE', 'ABBV', 'TMO', 'ABT', 'DHR', 'BMY', 'LLY', 'MRK',
            'AMGN', 'GILD', 'MDLZ', 'CVS', 'CI', 'HUM', 'ANTM', 'SYK', 'BSX', 'EW',
            
            # Consumer
            'HD', 'WMT', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'COST', 'SBUX', 'TGT',
            'LOW', 'DIS', 'CMCSA', 'VZ', 'T', 'NFLX', 'PYPL', 'MA', 'V', 'ADSK',
            
            # Industrial
            'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'RTX', 'LMT', 'NOC', 'GD',
            'DE', 'EMR', 'ETN', 'ITW', 'PH', 'CMI', 'FDX', 'WM', 'RSG', 'PCAR',
            
            # Energy & Materials
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO', 'PSX', 'KMI', 'OKE',
            'LIN', 'APD', 'ECL', 'SHW', 'DD', 'DOW', 'NEM', 'FCX', 'GOLD', 'AA',
            
            # Utilities & REITs
            'NEE', 'DUK', 'SO', 'D', 'EXC', 'XEL', 'SRE', 'AEP', 'ES', 'AWK',
            'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'EXR', 'AVB', 'EQR', 'UDR', 'ESS',
            
            # Additional liquid names
            'ROKU', 'ZM', 'DOCU', 'OKTA', 'SNOW', 'PLTR', 'RBLX', 'COIN', 'HOOD', 'SOFI'
        ]
    
    def _calculate_trade_return(self, entry_price: float, exit_price: float, 
                              stop_loss_price: float) -> Tuple[float, bool]:
        """
        Calculate trade return considering stop loss.
        
        Returns:
            Tuple of (return_pct, hit_stop_loss)
        """
        # Check if stop loss was hit (assuming worst case during hold period)
        if exit_price <= stop_loss_price:
            return_pct = (stop_loss_price - entry_price) / entry_price * 100
            return return_pct, True
        else:
            return_pct = (exit_price - entry_price) / entry_price * 100
            return return_pct, False
    
    def _get_spy_data(self) -> pd.DataFrame:
        """Get SPY data for market regime analysis."""
        try:
            spy_data = self.data_manager.get_historical_data('SPY', self.start_date, self.end_date)
            if not spy_data.empty:
                spy_data = self.indicators.add_all_indicators(spy_data)
            return spy_data
        except Exception as e:
            print(f"⚠️  Could not get SPY data: {e}")
            return pd.DataFrame()

    def test_rsi_plus_volume_spike(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Volume Spike (>1.5x average)."""
        signals = []

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # Volume spike condition
            if pd.isna(row.get('volume_ratio')) or row['volume_ratio'] < 1.5:
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'volume_ratio': row['volume_ratio'],
                'signal_type': 'RSI_VOLUME_SPIKE'
            })

        return signals

    def test_rsi_plus_macd_bullish(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + MACD above signal line."""
        signals = []

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # MACD bullish condition
            if (pd.isna(row.get('macd')) or pd.isna(row.get('macd_signal')) or
                row['macd'] <= row['macd_signal']):
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'macd': row['macd'],
                'macd_signal': row['macd_signal'],
                'signal_type': 'RSI_MACD_BULLISH'
            })

        return signals

    def test_rsi_plus_stoch_oversold(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Stochastic < 20 (double oversold)."""
        signals = []

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # Stochastic oversold condition
            if (pd.isna(row.get('stoch_k')) or pd.isna(row.get('stoch_d')) or
                row['stoch_k'] >= 20 or row['stoch_d'] >= 20):
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'stoch_k': row['stoch_k'],
                'stoch_d': row['stoch_d'],
                'signal_type': 'RSI_STOCH_OVERSOLD'
            })

        return signals

    def test_rsi_plus_bb_oversold(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Price below Bollinger Band lower."""
        signals = []

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # Bollinger Band oversold condition
            if pd.isna(row.get('bb_lower')) or row['close'] >= row['bb_lower']:
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'bb_position': (row['close'] - row['bb_lower']) / (row['bb_upper'] - row['bb_lower']),
                'signal_type': 'RSI_BB_OVERSOLD'
            })

        return signals

    def test_rsi_plus_ma_trend(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + Price above 20-day MA (pullback in uptrend)."""
        signals = []

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # Price above 20-day MA (uptrend pullback)
            if pd.isna(row.get('sma_20')) or row['close'] <= row['sma_20']:
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'ma_distance': (row['close'] - row['sma_20']) / row['sma_20'] * 100,
                'signal_type': 'RSI_MA_UPTREND'
            })

        return signals

    def test_rsi_plus_atr_volatility(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + High ATR (high volatility for bigger moves)."""
        signals = []

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # High ATR condition (above 50th percentile)
            if pd.isna(row.get('atr')):
                continue

            # Calculate ATR percentile for this stock
            atr_values = data['atr'].dropna()
            if len(atr_values) < 50:
                continue

            atr_percentile = (atr_values <= row['atr']).mean()
            if atr_percentile < 0.5:  # ATR below median
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'atr': row['atr'],
                'atr_percentile': atr_percentile,
                'signal_type': 'RSI_HIGH_ATR'
            })

        return signals

    def test_rsi_plus_spy_regime(self, data: pd.DataFrame, spy_data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + SPY bullish regime (SPY above 50-day MA)."""
        signals = []

        if spy_data.empty:
            return signals

        # Create SPY regime lookup
        spy_regime = {}
        for _, spy_row in spy_data.iterrows():
            if not pd.isna(spy_row.get('sma_50')):
                regime = 'bullish' if spy_row['close'] > spy_row['sma_50'] else 'bearish'
                spy_regime[spy_row['date']] = regime

        for i in range(len(data)):
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # SPY bullish regime condition
            regime = spy_regime.get(row['date'], 'unknown')
            if regime != 'bullish':
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'spy_regime': regime,
                'signal_type': 'RSI_SPY_BULLISH'
            })

        return signals

    def test_rsi_plus_obv_momentum(self, data: pd.DataFrame) -> List[Dict]:
        """Test RSI < 25 + OBV rising (volume momentum confirmation)."""
        signals = []

        for i in range(5, len(data)):  # Need 5 days for OBV trend
            row = data.iloc[i]

            # Base RSI condition
            if pd.isna(row.get('rsi')) or row['rsi'] >= self.rsi_threshold:
                continue

            # OBV rising condition (current > 5-day average)
            if pd.isna(row.get('obv')):
                continue

            obv_recent = data.iloc[i-5:i]['obv'].mean()
            if pd.isna(obv_recent) or row['obv'] <= obv_recent:
                continue

            # Valid signal
            signals.append({
                'date': row['date'],
                'entry_price': row['close'],
                'rsi': row['rsi'],
                'obv': row['obv'],
                'obv_trend': 'rising',
                'signal_type': 'RSI_OBV_RISING'
            })

        return signals

    def backtest_strategy(self, signals: List[Dict], data: pd.DataFrame,
                         strategy_name: str) -> Dict:
        """
        Backtest a strategy with given signals.

        Args:
            signals: List of signal dictionaries
            data: Stock price data
            strategy_name: Name of the strategy

        Returns:
            Dictionary with backtest results
        """
        if not signals:
            return {
                'strategy_name': strategy_name,
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'trades': []
            }

        trades = []

        for signal in signals:
            try:
                # Find entry date index
                entry_idx = data[data['date'] == signal['date']].index
                if len(entry_idx) == 0:
                    continue
                entry_idx = entry_idx[0]

                # Calculate exit date (2 trading days later)
                exit_idx = min(entry_idx + self.hold_days, len(data) - 1)

                entry_price = signal['entry_price']
                exit_price = data.iloc[exit_idx]['close']
                stop_loss_price = entry_price * (1 - self.stop_loss_pct / 100)

                # Calculate return
                trade_return, hit_stop = self._calculate_trade_return(
                    entry_price, exit_price, stop_loss_price
                )

                trade = {
                    'entry_date': signal['date'],
                    'exit_date': data.iloc[exit_idx]['date'],
                    'entry_price': entry_price,
                    'exit_price': exit_price if not hit_stop else stop_loss_price,
                    'return_pct': trade_return,
                    'hit_stop_loss': hit_stop,
                    'signal_data': signal
                }
                trades.append(trade)

            except Exception as e:
                continue

        # Calculate performance metrics
        if not trades:
            return {
                'strategy_name': strategy_name,
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0,
                'trades': []
            }

        returns = [t['return_pct'] for t in trades]
        winning_trades = [r for r in returns if r > 0]
        losing_trades = [r for r in returns if r <= 0]

        win_rate = len(winning_trades) / len(trades) * 100
        avg_return = np.mean(returns)
        total_return = sum(returns)

        # Profit factor
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 1
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Max drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0

        return {
            'strategy_name': strategy_name,
            'total_trades': len(trades),
            'win_rate': win_rate,
            'avg_return': avg_return,
            'profit_factor': profit_factor,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'trades': trades
        }

    def run_comprehensive_test(self) -> Dict:
        """
        Run comprehensive test of all RSI + indicator combinations.

        Returns:
            Dictionary with all test results
        """
        print("🔬 RSI + SECOND INDICATOR COMPREHENSIVE BACKTESTING")
        print("=" * 80)
        print(f"📊 Testing Period: {self.start_date} to {self.end_date}")
        print(f"📈 Stock Universe: {len(self.stock_universe)} stocks")
        print(f"🎯 Base Strategy: RSI < {self.rsi_threshold}, {self.hold_days}-day hold, {self.stop_loss_pct}% stop")
        print("=" * 80)

        # Get SPY data for regime analysis
        spy_data = self._get_spy_data()

        all_results = {}
        strategy_functions = {
            'RSI_VOLUME_SPIKE': self.test_rsi_plus_volume_spike,
            'RSI_MACD_BULLISH': self.test_rsi_plus_macd_bullish,
            'RSI_STOCH_OVERSOLD': self.test_rsi_plus_stoch_oversold,
            'RSI_BB_OVERSOLD': self.test_rsi_plus_bb_oversold,
            'RSI_MA_UPTREND': self.test_rsi_plus_ma_trend,
            'RSI_HIGH_ATR': self.test_rsi_plus_atr_volatility,
            'RSI_SPY_BULLISH': lambda data: self.test_rsi_plus_spy_regime(data, spy_data),
            'RSI_OBV_RISING': self.test_rsi_plus_obv_momentum
        }

        # Test each strategy
        for strategy_name, strategy_func in strategy_functions.items():
            print(f"\n🧪 Testing {strategy_name}...")

            all_signals = []
            processed_stocks = 0

            for i, ticker in enumerate(self.stock_universe):
                try:
                    if i % 25 == 0:
                        print(f"   Progress: {i}/{len(self.stock_universe)} stocks")

                    # Get stock data
                    data = self.data_manager.get_historical_data(ticker, self.start_date, self.end_date)

                    if data.empty or len(data) < 100:
                        continue

                    # Add all indicators
                    data = self.indicators.add_all_indicators(data)

                    # Test strategy
                    signals = strategy_func(data)

                    # Add ticker to signals
                    for signal in signals:
                        signal['ticker'] = ticker

                    all_signals.extend(signals)
                    processed_stocks += 1

                except Exception as e:
                    print(f"   ⚠️  Error processing {ticker}: {e}")
                    continue

            print(f"   ✅ Found {len(all_signals)} signals from {processed_stocks} stocks")

            # Backtest the strategy
            if all_signals:
                # For backtesting, we need to group signals by ticker and test each
                ticker_results = []

                for ticker in set(signal['ticker'] for signal in all_signals):
                    ticker_signals = [s for s in all_signals if s['ticker'] == ticker]

                    try:
                        data = self.data_manager.get_historical_data(ticker, self.start_date, self.end_date)
                        data = self.indicators.add_all_indicators(data)

                        result = self.backtest_strategy(ticker_signals, data, strategy_name)
                        ticker_results.append(result)

                    except Exception as e:
                        continue

                # Combine results
                combined_result = self._combine_ticker_results(ticker_results, strategy_name)
                all_results[strategy_name] = combined_result
            else:
                all_results[strategy_name] = {
                    'strategy_name': strategy_name,
                    'total_trades': 0,
                    'win_rate': 0,
                    'avg_return': 0,
                    'profit_factor': 0,
                    'total_return': 0,
                    'max_drawdown': 0
                }

        return all_results

    def _combine_ticker_results(self, ticker_results: List[Dict], strategy_name: str) -> Dict:
        """Combine results from multiple tickers into overall strategy performance."""
        if not ticker_results:
            return {
                'strategy_name': strategy_name,
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0
            }

        # Combine all trades
        all_trades = []
        for result in ticker_results:
            all_trades.extend(result.get('trades', []))

        if not all_trades:
            return {
                'strategy_name': strategy_name,
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'profit_factor': 0,
                'total_return': 0,
                'max_drawdown': 0
            }

        # Calculate combined metrics
        returns = [t['return_pct'] for t in all_trades]
        winning_trades = [r for r in returns if r > 0]
        losing_trades = [r for r in returns if r <= 0]

        win_rate = len(winning_trades) / len(all_trades) * 100
        avg_return = np.mean(returns)
        total_return = sum(returns)

        # Profit factor
        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 1
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Max drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0

        return {
            'strategy_name': strategy_name,
            'total_trades': len(all_trades),
            'win_rate': win_rate,
            'avg_return': avg_return,
            'profit_factor': profit_factor,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'trades': all_trades
        }

    def display_results(self, results: Dict):
        """Display comprehensive test results."""
        print("\n" + "=" * 80)
        print("📊 RSI + SECOND INDICATOR BACKTEST RESULTS")
        print("=" * 80)

        # Baseline comparison (original RSI strategy: 58.7% win rate)
        baseline_win_rate = 58.7
        baseline_profit_factor = 2.52
        baseline_avg_return = 2.03

        print(f"\n🎯 BASELINE (RSI Only): {baseline_win_rate}% win rate, {baseline_profit_factor:.2f} profit factor, {baseline_avg_return:.2f}% avg return")
        print("\n📈 ENHANCED STRATEGIES:")

        # Sort by win rate
        sorted_strategies = sorted(results.items(), key=lambda x: x[1]['win_rate'], reverse=True)

        for strategy_name, result in sorted_strategies:
            win_rate = result['win_rate']
            profit_factor = result['profit_factor']
            avg_return = result['avg_return']
            total_trades = result['total_trades']

            # Performance vs baseline
            win_rate_improvement = win_rate - baseline_win_rate
            pf_improvement = profit_factor - baseline_profit_factor
            return_improvement = avg_return - baseline_avg_return

            # Status indicators
            win_status = "🟢" if win_rate_improvement > 0 else "🔴" if win_rate_improvement < -2 else "🟡"
            pf_status = "🟢" if pf_improvement > 0 else "🔴" if pf_improvement < -0.2 else "🟡"

            print(f"\n{win_status} {strategy_name}:")
            print(f"   Win Rate: {win_rate:.1f}% ({win_rate_improvement:+.1f}%)")
            print(f"   Profit Factor: {pf_status} {profit_factor:.2f} ({pf_improvement:+.2f})")
            print(f"   Avg Return: {avg_return:.2f}% ({return_improvement:+.2f}%)")
            print(f"   Total Trades: {total_trades}")

            if total_trades < 100:
                print(f"   ⚠️  Low sample size - results may not be reliable")

        # Find best strategy
        best_strategy = max(results.items(), key=lambda x: x[1]['win_rate'])
        if best_strategy[1]['total_trades'] > 0:
            print(f"\n🏆 BEST STRATEGY: {best_strategy[0]}")
            print(f"   Win Rate: {best_strategy[1]['win_rate']:.1f}%")
            print(f"   Improvement: +{best_strategy[1]['win_rate'] - baseline_win_rate:.1f}%")
            print(f"   Total Trades: {best_strategy[1]['total_trades']}")

        print("\n" + "=" * 80)

    def save_results(self, results: Dict):
        """Save results to JSON file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"../results/rsi_plus_indicator_backtest_{timestamp}.json"

        # Prepare results for JSON (remove trades for file size)
        json_results = {}
        for strategy_name, result in results.items():
            json_result = result.copy()
            json_result.pop('trades', None)  # Remove detailed trades
            json_results[strategy_name] = json_result

        # Add metadata
        json_results['metadata'] = {
            'test_date': datetime.now().isoformat(),
            'test_period': f"{self.start_date} to {self.end_date}",
            'stock_universe_size': len(self.stock_universe),
            'base_strategy': f"RSI < {self.rsi_threshold}, {self.hold_days}-day hold, {self.stop_loss_pct}% stop",
            'baseline_performance': {
                'win_rate': 58.7,
                'profit_factor': 2.52,
                'avg_return': 2.03
            }
        }

        try:
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            with open(filename, 'w') as f:
                json.dump(json_results, f, indent=2)
            print(f"\n💾 Results saved to: {filename}")
        except Exception as e:
            print(f"⚠️  Could not save results: {e}")


def main():
    """Run the comprehensive RSI + indicator backtesting."""
    print("🚀 STARTING RSI + SECOND INDICATOR RESEARCH")
    print("Testing 8 different combinations to improve 58.7% win rate")
    print("This will take 15-30 minutes depending on API speed...")

    backtester = RSIPlusIndicatorBacktester()

    # Run comprehensive test
    results = backtester.run_comprehensive_test()

    # Display results
    backtester.display_results(results)

    # Save results
    backtester.save_results(results)

    print("\n✅ RSI + Second Indicator research completed!")
    print("💡 Check results above to see which combination works best")


if __name__ == "__main__":
    main()
