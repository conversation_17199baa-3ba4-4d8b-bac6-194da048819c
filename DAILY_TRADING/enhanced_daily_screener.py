#!/usr/bin/env python3
"""
Enhanced Daily RSI Oversold Screener with Snapshots
===================================================

Enhanced screening tool using Polygon's snapshot API for faster bulk data retrieval.

Features:
- Bulk snapshot API for 100+ stocks in one call
- Real-time RSI calculation from current market data
- Faster screening with reduced API calls
- Same proven RSI Oversold Bounce strategy (58.7% win rate)

Usage:
    python3 enhanced_daily_screener.py
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system', 'src'))

from rsi_oversold_strategy import RSIOversoldStrategy
from api.polygon_client import PolygonClient
from analysis.indicators import TechnicalIndicators

class EnhancedRSIScreener:
    """Enhanced RSI screener using snapshot API for faster bulk screening."""
    
    def __init__(self):
        """Initialize the enhanced screener."""
        self.client = PolygonClient()
        self.indicators = TechnicalIndicators()
        self.strategy = RSIOversoldStrategy()
        
        # Portfolio configuration
        self.portfolio_value = 813.65  # USD
        
        # Enhanced screening universe (100 top liquid stocks)
        self.screening_universe = [
            # Technology (25 stocks)
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX', 'ADBE', 'CRM',
            'ORCL', 'CSCO', 'INTC', 'AMD', 'QCOM', 'TXN', 'AVGO', 'INTU', 'NOW', 'PANW',
            'SNOW', 'DDOG', 'ZM', 'OKTA', 'CRWD',
            
            # Financial (20 stocks)
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SPGI', 'V',
            'MA', 'COF', 'USB', 'TFC', 'PNC', 'SCHW', 'CB', 'MMC', 'AON', 'ICE',
            
            # Healthcare (20 stocks)
            'UNH', 'JNJ', 'PFE', 'ABBV', 'MRK', 'TMO', 'ABT', 'LLY', 'MDT', 'BMY',
            'AMGN', 'GILD', 'CVS', 'CI', 'HUM', 'SYK', 'BSX', 'EW', 'ZTS', 'REGN',
            
            # Consumer Discretionary (20 stocks)
            'HD', 'MCD', 'NKE', 'LOW', 'SBUX', 'TJX', 'BKNG', 'CMG', 'ORLY', 'AZO',
            'ULTA', 'RCL', 'CCL', 'MAR', 'HLT', 'MGM', 'WYNN', 'LVS', 'YUM', 'QSR',
            
            # Consumer Staples (15 stocks)
            'WMT', 'PG', 'KO', 'PEP', 'COST', 'CL', 'KMB', 'GIS', 'K', 'HSY',
            'MDLZ', 'CPB', 'CAG', 'SJM', 'TAP'
        ]

    def get_bulk_market_data(self, tickers: List[str]) -> Dict:
        """
        Get current market data for all tickers using snapshot API.
        
        Args:
            tickers: List of ticker symbols
            
        Returns:
            Dictionary with current market data for each ticker
        """
        print(f"📊 Getting market snapshots for {len(tickers)} stocks...")
        
        try:
            # Get snapshots for all tickers in one API call
            snapshots = self.client.get_market_snapshot(tickers)
            
            if not snapshots:
                print("❌ No snapshot data received")
                return {}
            
            print(f"✅ Retrieved snapshots for {len(snapshots)} stocks")
            return snapshots
            
        except Exception as e:
            print(f"❌ Error getting market snapshots: {str(e)}")
            return {}

    def calculate_rsi_from_historical(self, ticker: str, current_price: float) -> float:
        """
        Calculate RSI using historical data and current price.
        
        Args:
            ticker: Stock ticker symbol
            current_price: Current market price
            
        Returns:
            Current RSI value
        """
        try:
            # Get 30 days of historical data for RSI calculation
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=45)).strftime('%Y-%m-%d')
            
            historical_data = self.client.get_historical_data(
                ticker, start_date, end_date, timespan='day'
            )
            
            if historical_data.empty or len(historical_data) < 14:
                return np.nan
            
            # Add current price as the latest data point
            current_row = pd.DataFrame({
                'close': [current_price],
                'timestamp': [datetime.now().timestamp() * 1000]
            })
            
            # Combine historical and current data
            combined_data = pd.concat([historical_data, current_row], ignore_index=True)
            
            # Calculate RSI using pandas Series
            rsi_series = self.indicators.rsi(combined_data['close'])

            # Return the latest RSI value
            latest_rsi = rsi_series.iloc[-1] if not rsi_series.empty else np.nan
            return latest_rsi if not np.isnan(latest_rsi) else np.nan
            
        except Exception as e:
            print(f"⚠️  Error calculating RSI for {ticker}: {str(e)}")
            return np.nan

    def screen_with_snapshots(self) -> List[Dict]:
        """
        Screen for RSI < 25 signals using snapshot API for efficiency.
        
        Returns:
            List of signal dictionaries
        """
        print("🔍 ENHANCED RSI SCREENING WITH SNAPSHOTS")
        print("=" * 60)
        
        # Get bulk market data
        market_data = self.get_bulk_market_data(self.screening_universe)
        
        if not market_data:
            print("❌ No market data available for screening")
            return []
        
        signals = []
        processed_count = 0
        
        print(f"📈 Calculating RSI for {len(market_data)} stocks...")
        
        for ticker, snapshot in market_data.items():
            try:
                current_price = snapshot.get('current_price')
                if not current_price or current_price <= 0:
                    continue
                
                # Calculate RSI using historical data + current price
                rsi = self.calculate_rsi_from_historical(ticker, current_price)
                
                if np.isnan(rsi):
                    continue
                
                processed_count += 1
                
                # Check for RSI oversold signal (RSI < 25)
                if rsi < 25:
                    # Calculate signal strength based on how oversold
                    if rsi < 15:
                        signal_strength = "VERY STRONG"
                    elif rsi < 20:
                        signal_strength = "STRONG"
                    else:
                        signal_strength = "MODERATE"
                    
                    signal = {
                        'ticker': ticker,
                        'price': current_price,  # Use 'price' key for compatibility
                        'current_price': current_price,
                        'rsi': rsi,
                        'signal_strength': signal_strength,
                        'volume': snapshot.get('volume', 0),
                        'change_percent': snapshot.get('change_percent', 0),
                        'timestamp': datetime.now().isoformat(),
                        'date': datetime.now().strftime('%Y-%m-%d'),  # Add date field

                        # Strategy expectations (from research)
                        'expected_return': 2.03,  # Average return from research
                        'win_probability': 58.7,  # Win rate from research
                        'profit_factor': 2.52,   # Profit factor from research
                        'holding_period': 2,     # Days to hold
                        'stop_loss_percent': 2.0 # Stop loss percentage
                    }
                    
                    signals.append(signal)
                    print(f"🎯 SIGNAL: {ticker} - RSI {rsi:.1f} ({signal_strength}) - ${current_price:.2f}")
                
                # Progress indicator
                if processed_count % 20 == 0:
                    print(f"   Processed {processed_count}/{len(market_data)} stocks...")
                    
            except Exception as e:
                print(f"⚠️  Error processing {ticker}: {str(e)}")
                continue
        
        # Sort signals by RSI (most oversold first)
        signals.sort(key=lambda x: x['rsi'])
        
        print(f"\n✅ Screening complete: {processed_count} stocks processed")
        print(f"🎯 Found {len(signals)} RSI oversold signals")
        
        return signals

    def generate_trade_plans(self, signals: List[Dict]) -> List[Dict]:
        """Generate trade plans for all signals."""
        trade_plans = []
        
        if not signals:
            return trade_plans
        
        print(f"\n📋 GENERATING TRADE PLANS FOR {len(signals)} SIGNALS...")
        
        for i, signal in enumerate(signals[:5]):  # Limit to top 5 signals
            trade_plan = self.strategy.generate_trade_plan(signal, self.portfolio_value)
            trade_plans.append(trade_plan)
            
            print(f"\n🎯 TRADE PLAN #{i+1}: {signal['ticker']}")
            self.strategy.display_trade_plan(trade_plan)
        
        return trade_plans

    def save_results(self, signals: List[Dict], trade_plans: List[Dict]):
        """Save screening results to daily_results directory."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Ensure daily_results directory exists
        os.makedirs('../results/daily_results', exist_ok=True)
        
        results = {
            'timestamp': timestamp,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'strategy': 'RSI Oversold Bounce (Enhanced with Snapshots)',
            'screening_method': 'Polygon Snapshot API',
            'research_basis': {
                'win_rate': 58.7,
                'profit_factor': 2.52,
                'avg_return': 2.03,
                'sample_size': 5256,
                'research_date': '2025-06-09'
            },
            'screening_stats': {
                'stocks_screened': len(self.screening_universe),
                'signals_found': len(signals),
                'trade_plans_generated': len(trade_plans)
            },
            'signals': signals,
            'trade_plans': trade_plans
        }
        
        filename = f"../results/daily_results/enhanced_rsi_screening_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Results saved to: {filename}")
        return filename

    def display_summary(self, signals: List[Dict], trade_plans: List[Dict]):
        """Display screening summary."""
        print("\n" + "=" * 80)
        print("📊 ENHANCED DAILY SCREENING SUMMARY")
        print("=" * 80)
        print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔍 Stocks Screened: {len(self.screening_universe)} (via Snapshot API)")
        print(f"🎯 Signals Found: {len(signals)}")
        print(f"📋 Trade Plans Generated: {len(trade_plans)}")
        
        if signals:
            best_signal = signals[0]
            print(f"\n🏆 BEST OPPORTUNITY:")
            print(f"   Ticker: {best_signal['ticker']}")
            print(f"   RSI: {best_signal['rsi']:.1f}")
            print(f"   Signal Strength: {best_signal['signal_strength']}")
            print(f"   Current Price: ${best_signal['current_price']:.2f}")
            print(f"   Expected Return: {best_signal['expected_return']:.2f}%")
            print(f"   Win Probability: {best_signal['win_probability']:.1f}%")
            
            print(f"\n📋 NEXT STEPS:")
            print(f"   1. Review trade plan for {best_signal['ticker']}")
            print(f"   2. Prepare to buy at market open")
            print(f"   3. Set stop loss at 2% below entry")
            print(f"   4. Set calendar reminder to sell in 2 days")
            print(f"   5. Expected profit: ~2.03% (${self.portfolio_value * 0.0203:.2f})")
        else:
            print(f"\n❌ NO SIGNALS FOUND TODAY")
            print(f"   No stocks with RSI < 25")
            print(f"   Check again tomorrow")
            print(f"   Typical frequency: 4-8 signals per month")
        
        print(f"\n✅ Enhanced screening completed!")
        print(f"💡 Remember: This strategy has 58.7% win rate based on 5,256 real trades")
        print(f"🚀 Enhancement: Using Snapshot API for faster bulk screening")

def main():
    """Run enhanced daily RSI oversold screening."""
    print("=" * 80)
    print("📅 ENHANCED DAILY RSI OVERSOLD SCREENER")
    print("🚀 Powered by Polygon Snapshot API")
    print("Strategy: 58.7% Win Rate | 2.52 Profit Factor | 2.03% Avg Return")
    print("Research: 5 Years | 150 Stocks | 5,256 Trades | June 9, 2025")
    print("=" * 80)
    
    # Initialize enhanced screener
    screener = EnhancedRSIScreener()
    
    # Screen for signals using snapshots
    signals = screener.screen_with_snapshots()
    
    # Generate trade plans
    trade_plans = screener.generate_trade_plans(signals)
    
    # Save results
    screener.save_results(signals, trade_plans)
    
    # Display summary
    screener.display_summary(signals, trade_plans)

if __name__ == "__main__":
    main()
