#!/usr/bin/env python3
"""
RSI Oversold Position Tracker
==============================

Track active RSI Oversold Bounce positions and manage 2-day exits.

Features:
- Track active positions
- Monitor 2-day holding periods
- Calculate actual vs expected returns
- Performance tracking vs 58.7% baseline
- Exit alerts and reminders
"""

import sys
import os
import json
from datetime import datetime, timedelta
import pandas as pd

# Add system path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'system', 'src'))

from data.data_manager import DataManager

class RSIOversoldTracker:
    """Track RSI Oversold Bounce positions."""
    
    def __init__(self):
        """Initialize the position tracker."""
        self.data_manager = DataManager()
        self.positions_file = "../results/daily_results/active_positions.json"
        self.performance_file = "../results/daily_results/strategy_performance.json"
        
        # Strategy benchmarks
        self.expected_win_rate = 58.7
        self.expected_avg_return = 2.03
        self.expected_profit_factor = 2.52
    
    def add_position(self, trade_plan: dict):
        """Add a new position to tracking."""
        positions = self._load_positions()
        
        position = {
            'id': f"{trade_plan['ticker']}_{trade_plan['entry_date']}",
            'ticker': trade_plan['ticker'],
            'entry_date': str(trade_plan['entry_date']),
            'exit_date': str(trade_plan['exit_date']),
            'entry_price': trade_plan['entry_price'],
            'shares': trade_plan['shares'],
            'position_value': trade_plan['position_value'],
            'stop_loss_price': trade_plan['stop_loss_price'],
            'rsi_at_entry': trade_plan['rsi'],
            'signal_strength': trade_plan['signal_strength'],
            'expected_return_pct': trade_plan['expected_return_pct'],
            'expected_return_usd': trade_plan['expected_return_usd'],
            'status': 'ACTIVE',
            'days_held': 0,
            'current_price': trade_plan['entry_price'],
            'current_return_pct': 0.0,
            'current_return_usd': 0.0,
            'max_gain_pct': 0.0,
            'max_loss_pct': 0.0,
            'added_timestamp': datetime.now().isoformat()
        }
        
        positions[position['id']] = position
        self._save_positions(positions)
        
        print(f"✅ Added position: {trade_plan['ticker']} to tracking")
        return position['id']
    
    def update_positions(self):
        """Update all active positions with current prices."""
        positions = self._load_positions()
        active_positions = {k: v for k, v in positions.items() if v['status'] == 'ACTIVE'}
        
        if not active_positions:
            print("📊 No active positions to update")
            return
        
        print(f"🔄 Updating {len(active_positions)} active positions...")
        
        current_date = datetime.now().date()
        
        for pos_id, position in active_positions.items():
            try:
                ticker = position['ticker']
                entry_date = datetime.strptime(position['entry_date'], '%Y-%m-%d').date()
                exit_date = datetime.strptime(position['exit_date'], '%Y-%m-%d').date()
                
                # Calculate days held
                days_held = (current_date - entry_date).days
                position['days_held'] = days_held
                
                # Get current price
                end_date = current_date + timedelta(days=1)  # Include today
                data = self.data_manager.get_historical_data(ticker, entry_date, end_date)
                
                if not data.empty:
                    current_price = data.iloc[-1]['close']
                    position['current_price'] = current_price
                    
                    # Calculate returns
                    entry_price = position['entry_price']
                    return_pct = ((current_price / entry_price) - 1) * 100
                    return_usd = position['shares'] * (current_price - entry_price)
                    
                    position['current_return_pct'] = return_pct
                    position['current_return_usd'] = return_usd
                    
                    # Update max gain/loss
                    position['max_gain_pct'] = max(position.get('max_gain_pct', 0), return_pct)
                    position['max_loss_pct'] = min(position.get('max_loss_pct', 0), return_pct)
                    
                    # Check if position should be closed
                    if current_date >= exit_date:
                        position['status'] = 'READY_TO_CLOSE'
                        position['exit_reason'] = '2_DAY_HOLD_COMPLETE'
                    elif current_price <= position['stop_loss_price']:
                        position['status'] = 'STOP_LOSS_HIT'
                        position['exit_reason'] = 'STOP_LOSS'
                
                positions[pos_id] = position
                
            except Exception as e:
                print(f"   ⚠️  Error updating {position['ticker']}: {e}")
        
        self._save_positions(positions)
        print("✅ Position updates completed")
    
    def close_position(self, position_id: str, exit_price: float = None, exit_reason: str = "MANUAL"):
        """Close a position and record the trade."""
        positions = self._load_positions()
        
        if position_id not in positions:
            print(f"❌ Position {position_id} not found")
            return
        
        position = positions[position_id]
        
        if exit_price is None:
            exit_price = position['current_price']
        
        # Calculate final returns
        entry_price = position['entry_price']
        final_return_pct = ((exit_price / entry_price) - 1) * 100
        final_return_usd = position['shares'] * (exit_price - entry_price)
        
        # Update position
        position['status'] = 'CLOSED'
        position['exit_price'] = exit_price
        position['exit_reason'] = exit_reason
        position['final_return_pct'] = final_return_pct
        position['final_return_usd'] = final_return_usd
        position['closed_timestamp'] = datetime.now().isoformat()
        
        # Determine if trade was winner
        position['is_winner'] = final_return_pct > 0
        
        positions[position_id] = position
        self._save_positions(positions)
        
        # Update performance tracking
        self._update_performance(position)
        
        print(f"✅ Closed position: {position['ticker']}")
        print(f"   Return: {final_return_pct:.2f}% (${final_return_usd:.2f})")
        print(f"   Result: {'WIN' if position['is_winner'] else 'LOSS'}")
        
        return position
    
    def get_active_positions(self) -> dict:
        """Get all active positions."""
        positions = self._load_positions()
        return {k: v for k, v in positions.items() if v['status'] == 'ACTIVE'}
    
    def get_positions_ready_to_close(self) -> dict:
        """Get positions ready to close (2 days completed)."""
        positions = self._load_positions()
        return {k: v for k, v in positions.items() if v['status'] == 'READY_TO_CLOSE'}
    
    def display_active_positions(self):
        """Display all active positions."""
        active = self.get_active_positions()
        ready_to_close = self.get_positions_ready_to_close()
        
        print("\n" + "=" * 80)
        print("📊 ACTIVE RSI OVERSOLD POSITIONS")
        print("=" * 80)
        
        if not active and not ready_to_close:
            print("📭 No active positions")
            return
        
        # Active positions
        if active:
            print(f"\n🔄 ACTIVE POSITIONS ({len(active)}):")
            print(f"{'Ticker':<8} {'Days':<5} {'Entry':<8} {'Current':<8} {'Return':<8} {'Status':<12}")
            print("-" * 80)
            
            for position in active.values():
                print(f"{position['ticker']:<8} "
                      f"{position['days_held']:<5} "
                      f"${position['entry_price']:<7.2f} "
                      f"${position['current_price']:<7.2f} "
                      f"{position['current_return_pct']:<7.2f}% "
                      f"{position['status']:<12}")
        
        # Ready to close
        if ready_to_close:
            print(f"\n🎯 READY TO CLOSE ({len(ready_to_close)}):")
            print(f"{'Ticker':<8} {'Days':<5} {'Entry':<8} {'Current':<8} {'Return':<8} {'Action':<12}")
            print("-" * 80)
            
            for position in ready_to_close.values():
                print(f"{position['ticker']:<8} "
                      f"{position['days_held']:<5} "
                      f"${position['entry_price']:<7.2f} "
                      f"${position['current_price']:<7.2f} "
                      f"{position['current_return_pct']:<7.2f}% "
                      f"{'SELL NOW':<12}")
    
    def display_performance_summary(self):
        """Display strategy performance vs expectations."""
        performance = self._load_performance()
        
        if not performance.get('trades'):
            print("\n📊 No completed trades yet")
            return
        
        trades = performance['trades']
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['is_winner']])
        win_rate = (winning_trades / total_trades) * 100
        
        total_return = sum([t['final_return_pct'] for t in trades])
        avg_return = total_return / total_trades
        
        gross_profit = sum([t['final_return_pct'] for t in trades if t['final_return_pct'] > 0])
        gross_loss = abs(sum([t['final_return_pct'] for t in trades if t['final_return_pct'] < 0]))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        print("\n" + "=" * 60)
        print("📊 RSI OVERSOLD STRATEGY PERFORMANCE")
        print("=" * 60)
        print(f"📈 Total Trades: {total_trades}")
        print(f"🏆 Win Rate: {win_rate:.1f}% (Expected: {self.expected_win_rate:.1f}%)")
        print(f"📊 Avg Return: {avg_return:.2f}% (Expected: {self.expected_avg_return:.2f}%)")
        print(f"💰 Profit Factor: {profit_factor:.2f} (Expected: {self.expected_profit_factor:.2f})")
        print(f"📈 Total Return: {total_return:.2f}%")
        
        # Performance vs expectations
        win_rate_diff = win_rate - self.expected_win_rate
        avg_return_diff = avg_return - self.expected_avg_return
        
        print(f"\n📊 VS RESEARCH EXPECTATIONS:")
        print(f"   Win Rate: {win_rate_diff:+.1f}% {'✅' if win_rate_diff >= 0 else '❌'}")
        print(f"   Avg Return: {avg_return_diff:+.2f}% {'✅' if avg_return_diff >= 0 else '❌'}")
    
    def _load_positions(self) -> dict:
        """Load positions from file."""
        try:
            if os.path.exists(self.positions_file):
                with open(self.positions_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️  Error loading positions: {e}")
        return {}
    
    def _save_positions(self, positions: dict):
        """Save positions to file."""
        os.makedirs(os.path.dirname(self.positions_file), exist_ok=True)
        with open(self.positions_file, 'w') as f:
            json.dump(positions, f, indent=2, default=str)
    
    def _load_performance(self) -> dict:
        """Load performance data."""
        try:
            if os.path.exists(self.performance_file):
                with open(self.performance_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"⚠️  Error loading performance: {e}")
        return {'trades': [], 'summary': {}}
    
    def _update_performance(self, closed_position: dict):
        """Update performance tracking with closed trade."""
        performance = self._load_performance()
        
        trade_record = {
            'ticker': closed_position['ticker'],
            'entry_date': closed_position['entry_date'],
            'exit_date': closed_position.get('exit_date'),
            'entry_price': closed_position['entry_price'],
            'exit_price': closed_position.get('exit_price'),
            'final_return_pct': closed_position['final_return_pct'],
            'final_return_usd': closed_position['final_return_usd'],
            'is_winner': closed_position['is_winner'],
            'days_held': closed_position['days_held'],
            'rsi_at_entry': closed_position['rsi_at_entry'],
            'exit_reason': closed_position.get('exit_reason'),
            'closed_timestamp': closed_position['closed_timestamp']
        }
        
        performance['trades'].append(trade_record)
        
        # Update summary
        trades = performance['trades']
        performance['summary'] = {
            'total_trades': len(trades),
            'winning_trades': len([t for t in trades if t['is_winner']]),
            'win_rate': (len([t for t in trades if t['is_winner']]) / len(trades)) * 100,
            'avg_return': sum([t['final_return_pct'] for t in trades]) / len(trades),
            'total_return': sum([t['final_return_pct'] for t in trades]),
            'last_updated': datetime.now().isoformat()
        }
        
        os.makedirs(os.path.dirname(self.performance_file), exist_ok=True)
        with open(self.performance_file, 'w') as f:
            json.dump(performance, f, indent=2, default=str)


def main():
    """Run position tracking interface."""
    tracker = RSIOversoldTracker()
    
    print("📊 RSI OVERSOLD POSITION TRACKER")
    print("Strategy: 58.7% Win Rate | 2.52 Profit Factor")
    
    # Update all positions
    tracker.update_positions()
    
    # Display current status
    tracker.display_active_positions()
    tracker.display_performance_summary()
    
    print("\n✅ Position tracking completed!")


if __name__ == "__main__":
    main()
