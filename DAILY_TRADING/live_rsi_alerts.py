#!/usr/bin/env python3
"""
Live RSI Alert System
=====================

Continuous monitoring for new RSI < 25 opportunities using WebSocket streams.

Features:
- Real-time RSI calculation from minute bars
- Instant alerts when RSI drops below 25
- Background monitoring during market hours
- Trade plan generation for new signals
- WebSocket-powered continuous screening

Usage:
    python3 live_rsi_alerts.py
"""

import asyncio
import json
import os
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Deque
from collections import deque
from src.api.polygon_websocket import PolygonWebSocketClient
from src.analysis.technical_indicators import TechnicalIndicators
from rsi_oversold_strategy import RSIOversoldStrategy

class LiveRSIAlertSystem:
    """Live RSI alert system using WebSocket minute aggregates."""
    
    def __init__(self):
        """Initialize the live alert system."""
        self.ws_client = PolygonWebSocketClient()
        self.indicators = TechnicalIndicators()
        self.strategy = RSIOversoldStrategy()
        
        # Price history for RSI calculation (14 periods minimum)
        self.price_history: Dict[str, Deque] = {}
        self.rsi_history: Dict[str, Deque] = {}
        
        # Alert tracking
        self.alerts_sent = set()
        self.last_alert_time = {}
        
        # Configuration
        self.rsi_threshold = 25
        self.rsi_period = 14
        self.max_history_length = 50  # Keep 50 periods of history
        self.alert_cooldown_minutes = 60  # Don't repeat alerts for 1 hour
        
        # Portfolio configuration
        self.portfolio_value = 813.65  # USD
        
        # Watchlist for monitoring
        self.watchlist = [
            # High-volume, liquid stocks for best signals
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX',
            'JPM', 'BAC', 'WFC', 'GS', 'UNH', 'JNJ', 'PFE', 'HD', 'MCD',
            'WMT', 'PG', 'KO', 'PEP', 'COST', 'V', 'MA', 'ADBE', 'CRM'
        ]
        
        # Setup event handlers
        self._setup_handlers()

    def _setup_handlers(self):
        """Setup WebSocket event handlers."""
        self.ws_client.add_minute_agg_handler(self._handle_minute_aggregate)
        self.ws_client.add_error_handler(self._handle_error)

    async def _handle_minute_aggregate(self, minute_data: Dict):
        """Handle real-time minute aggregate data."""
        ticker = minute_data.get('ticker')
        close_price = minute_data.get('close')
        timestamp = minute_data.get('timestamp')
        
        if not ticker or not close_price:
            return
        
        # Initialize price history for new tickers
        if ticker not in self.price_history:
            self.price_history[ticker] = deque(maxlen=self.max_history_length)
            self.rsi_history[ticker] = deque(maxlen=self.max_history_length)
        
        # Add new price to history
        self.price_history[ticker].append(close_price)
        
        # Calculate RSI if we have enough data
        if len(self.price_history[ticker]) >= self.rsi_period:
            prices = list(self.price_history[ticker])
            rsi_values = self.indicators.calculate_rsi(np.array(prices), period=self.rsi_period)
            current_rsi = rsi_values[-1]
            
            # Add RSI to history
            self.rsi_history[ticker].append(current_rsi)
            
            # Check for RSI oversold signal
            if current_rsi < self.rsi_threshold:
                await self._check_rsi_alert(ticker, current_rsi, close_price, timestamp)
            
            # Display real-time RSI update
            self._display_rsi_update(ticker, current_rsi, close_price)

    async def _check_rsi_alert(self, ticker: str, rsi: float, price: float, timestamp: int):
        """Check if we should send an RSI alert."""
        current_time = datetime.now()
        alert_key = f"{ticker}_{current_time.strftime('%Y%m%d_%H')}"  # One alert per hour
        
        # Check cooldown period
        if ticker in self.last_alert_time:
            time_since_last = current_time - self.last_alert_time[ticker]
            if time_since_last.total_seconds() < (self.alert_cooldown_minutes * 60):
                return  # Still in cooldown period
        
        # Check if we already sent this alert
        if alert_key in self.alerts_sent:
            return
        
        # Send new RSI alert
        await self._send_rsi_alert(ticker, rsi, price, timestamp)
        
        # Update tracking
        self.alerts_sent.add(alert_key)
        self.last_alert_time[ticker] = current_time

    async def _send_rsi_alert(self, ticker: str, rsi: float, price: float, timestamp: int):
        """Send RSI oversold alert."""
        # Determine signal strength
        if rsi < 15:
            signal_strength = "VERY STRONG"
            strength_emoji = "🔥"
        elif rsi < 20:
            signal_strength = "STRONG"
            strength_emoji = "⚡"
        else:
            signal_strength = "MODERATE"
            strength_emoji = "📊"
        
        print("\n" + "🎯" * 20)
        print(f"🎯 NEW RSI OVERSOLD SIGNAL {strength_emoji}")
        print("🎯" * 20)
        print(f"Ticker: {ticker}")
        print(f"RSI: {rsi:.1f}")
        print(f"Signal Strength: {signal_strength}")
        print(f"Current Price: ${price:.2f}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Expected Return: 2.03% (${self.portfolio_value * 0.0203:.2f})")
        print(f"Win Probability: 58.7%")
        print("🎯" * 20)
        
        # Generate trade plan
        signal_data = {
            'ticker': ticker,
            'current_price': price,
            'rsi': rsi,
            'signal_strength': signal_strength,
            'timestamp': datetime.now().isoformat(),
            'expected_return': 2.03,
            'win_probability': 58.7,
            'profit_factor': 2.52,
            'holding_period': 2,
            'stop_loss_percent': 2.0
        }
        
        trade_plan = self.strategy.generate_trade_plan(signal_data, self.portfolio_value)
        
        print(f"\n📋 INSTANT TRADE PLAN:")
        self.strategy.display_trade_plan(trade_plan)
        
        # Save alert and trade plan
        await self._save_alert(ticker, signal_data, trade_plan)

    async def _save_alert(self, ticker: str, signal_data: Dict, trade_plan: Dict):
        """Save alert and trade plan to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        alert_data = {
            'timestamp': timestamp,
            'alert_type': 'RSI_OVERSOLD_SIGNAL',
            'signal': signal_data,
            'trade_plan': trade_plan,
            'strategy_basis': {
                'win_rate': 58.7,
                'profit_factor': 2.52,
                'avg_return': 2.03,
                'research_date': '2025-06-09'
            }
        }
        
        # Ensure daily_results directory exists
        os.makedirs('daily_results', exist_ok=True)
        
        filename = f"daily_results/live_rsi_alert_{ticker}_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(alert_data, f, indent=2, default=str)
        
        print(f"💾 Alert saved to: {filename}")

    async def _handle_error(self, error_data: Dict):
        """Handle WebSocket errors."""
        print(f"❌ WebSocket Error: {error_data.get('error', 'Unknown error')}")

    def _display_rsi_update(self, ticker: str, rsi: float, price: float):
        """Display real-time RSI update."""
        current_time = datetime.now().strftime('%H:%M:%S')
        
        # Color coding for RSI levels
        if rsi < 25:
            status = "🔴 OVERSOLD"
        elif rsi < 30:
            status = "🟡 APPROACHING"
        else:
            status = "🟢 NORMAL"
        
        print(f"{current_time} | {ticker:6} | RSI: {rsi:5.1f} | ${price:7.2f} | {status}")

    async def start_monitoring(self):
        """Start live RSI monitoring."""
        print("=" * 80)
        print("🎯 LIVE RSI ALERT SYSTEM")
        print("🚀 Powered by Polygon WebSocket Minute Aggregates")
        print("=" * 80)
        print(f"📊 Strategy: RSI Oversold Bounce (58.7% win rate)")
        print(f"🎯 Alert Threshold: RSI < {self.rsi_threshold}")
        print(f"📈 Monitoring: {len(self.watchlist)} stocks")
        print(f"⏰ Alert Cooldown: {self.alert_cooldown_minutes} minutes")
        
        # Connect to WebSocket
        print(f"\n🔌 Connecting to Polygon WebSocket...")
        connected = await self.ws_client.connect()
        
        if not connected:
            print("❌ Failed to connect to WebSocket")
            return
        
        # Subscribe to minute aggregates for watchlist
        print(f"📡 Subscribing to minute aggregates for {len(self.watchlist)} stocks...")
        await self.ws_client.subscribe_to_minute_aggregates(self.watchlist)
        
        print(f"\n✅ Live RSI monitoring started!")
        print(f"🎯 Watching for RSI < {self.rsi_threshold} signals")
        print(f"💡 Press Ctrl+C to stop monitoring")
        print(f"\n📊 REAL-TIME RSI UPDATES:")
        print("-" * 80)
        
        # Start listening for real-time updates
        try:
            await self.ws_client.listen()
        except KeyboardInterrupt:
            print(f"\n⏹️  Live monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Monitoring error: {str(e)}")
        finally:
            await self.ws_client.disconnect()

    def display_current_status(self):
        """Display current RSI status for all monitored stocks."""
        if not self.rsi_history:
            print("📊 No RSI data available yet")
            return
        
        print(f"\n📊 CURRENT RSI STATUS:")
        print("-" * 60)
        
        for ticker in sorted(self.rsi_history.keys()):
            if self.rsi_history[ticker]:
                current_rsi = self.rsi_history[ticker][-1]
                current_price = self.price_history[ticker][-1]
                
                if current_rsi < 25:
                    status = "🔴 OVERSOLD"
                elif current_rsi < 30:
                    status = "🟡 WATCH"
                else:
                    status = "🟢 NORMAL"
                
                print(f"{ticker:6} | RSI: {current_rsi:5.1f} | ${current_price:7.2f} | {status}")

async def main():
    """Run live RSI alert system."""
    alert_system = LiveRSIAlertSystem()
    await alert_system.start_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
